<?php
/**
 * Configuración de consultas y reportes disponibles en el sistema
 *
 * Este archivo define todas las consultas y reportes disponibles en el sistema,
 * junto con sus permisos, categorías, títulos y descripciones.
 */

// Array de consultas disponibles
$consultas = [
    // Consulta de prueba para exportación a Excel
    'exportar_test' => [
        'titulo' => 'Prueba de Exportación a Excel',
        'descripcion' => 'Consulta de prueba para verificar la exportación a Excel',
        'permiso' => 'exportar_excel',
        'categoria' => 'Pruebas',
        'archivo' => 'consultas/exportar_test.php',
        'tipo' => 'php'
    ],

    // Reportes sin ajuste
    'falla_caja' => [
        'titulo' => 'Falla de Caja',
        'descripcion' => 'Reporte de falla de caja para empleados',
        'permiso' => 'ver_reporte_falla_caja',
        'categoria' => 'Sin Ajuste',
        'archivo' => 'scripts_sql/falla_caja.sql',
        'tipo' => 'sql'
    ],
    'san_nicolas' => [
        'titulo' => 'San Nicolás Mensual',
        'descripcion' => 'Reporte mensual de San Nicolás',
        'permiso' => 'ver_reporte_san_nicolas',
        'categoria' => 'Sin Ajuste',
        'archivo' => 'scripts_sql/san_nicolas.sql',
        'tipo' => 'sql'
    ],
    'hidraulica' => [
        'titulo' => 'Imponible Hidráulica',
        'descripcion' => 'Reporte de imponible hidráulica',
        'permiso' => 'ver_reporte_hidraulica',
        'categoria' => 'Sin Ajuste',
        'archivo' => 'scripts_sql/hidraulica.sql',
        'tipo' => 'sql'
    ],
    'imponible_general' => [
        'titulo' => 'Imponible General',
        'descripcion' => 'Reporte de imponible general para todos los empleados',
        'permiso' => 'ver_reporte_imponible_general',
        'categoria' => 'Sin Ajuste',
        'archivo' => 'consultas/imponible_general.php'
    ],
    'imponible_detallado' => [
        'titulo' => 'Imponible Detallado',
        'descripcion' => 'Reporte detallado de imponibles por empleado',
        'permiso' => 'ver_reporte_imponible_detallado',
        'categoria' => 'Sin Ajuste',
        'archivo' => 'consultas/imponible_detallado.php'
    ],

    // Reportes con ajuste
    'falla_caja_ajuste' => [
        'titulo' => 'Falla de Caja (Con Ajuste)',
        'descripcion' => 'Reporte de falla de caja con ajustes aplicados',
        'permiso' => 'ver_reporte_falla_caja_ajuste',
        'categoria' => 'Con Ajuste',
        'archivo' => 'consultas/falla_caja_ajuste.php'
    ],
    'san_nicolas_ajuste' => [
        'titulo' => 'San Nicolás Mensual (Con Ajuste)',
        'descripcion' => 'Reporte mensual de San Nicolás con ajustes aplicados',
        'permiso' => 'ver_reporte_san_nicolas_ajuste',
        'categoria' => 'Con Ajuste',
        'archivo' => 'consultas/san_nicolas_ajuste.php'
    ],
    'hidraulica_ajuste' => [
        'titulo' => 'Imponible Hidráulica (Con Ajuste)',
        'descripcion' => 'Reporte de imponible hidráulica con ajustes aplicados',
        'permiso' => 'ver_reporte_hidraulica_ajuste',
        'categoria' => 'Con Ajuste',
        'archivo' => 'consultas/hidraulica_ajuste.php'
    ],
    'imponible_general_ajuste' => [
        'titulo' => 'Imponible General (Con Ajuste)',
        'descripcion' => 'Reporte de imponible general con ajustes aplicados',
        'permiso' => 'ver_reporte_imponible_general_ajuste',
        'categoria' => 'Con Ajuste',
        'archivo' => 'consultas/imponible_general_ajuste.php'
    ],
    'imponible_detallado_ajuste' => [
        'titulo' => 'Imponible Detallado (Con Ajuste)',
        'descripcion' => 'Reporte detallado de imponibles por empleado con ajustes aplicados',
        'permiso' => 'ver_reporte_imponible_detallado_ajuste',
        'categoria' => 'Con Ajuste',
        'archivo' => 'consultas/imponible_detallado_ajuste.php'
    ],
];

// Función para obtener una consulta específica
function getConsulta($key) {
    global $consultas;
    return isset($consultas[$key]) ? $consultas[$key] : null;
}

// Función para obtener todas las consultas de una categoría
function getConsultasPorCategoria($categoria) {
    global $consultas;
    $result = [];

    foreach ($consultas as $key => $info) {
        if (isset($info['categoria']) && $info['categoria'] === $categoria) {
            $result[$key] = $info;
        }
    }

    return $result;
}

// Función para obtener todas las categorías disponibles
function getCategorias() {
    global $consultas;
    $categorias = [];

    foreach ($consultas as $info) {
        if (isset($info['categoria']) && !in_array($info['categoria'], $categorias)) {
            $categorias[] = $info['categoria'];
        }
    }

    return $categorias;
}
?>
