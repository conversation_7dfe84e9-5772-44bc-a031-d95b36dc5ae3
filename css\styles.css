/* Estilos generales */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f7f9fb;
    color: #333;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Encabezado */
header {
    background-color: #2c3e50;
    color: white;
    padding: 20px 0;
    margin-bottom: 30px;
    position: relative;
}

header h1 {
    margin: 0;
    padding: 0 20px;
}

/* Usuario logueado */
/* Estilos para el indicador de usuario */
.user-info-container {
    position: fixed;
    top: 10px;
    left: 10px;
    z-index: 9999;
}

.user-info {
    display: inline-flex;
    align-items: center;
    background-color: #e74c3c;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.4);
    font-family: Arial, sans-serif;
    font-weight: bold;
    text-transform: uppercase;
    border: 3px solid #f39c12;
    letter-spacing: 1px;
}

.user-avatar {
    width: 36px;
    height: 36px;
    background-color: #2c3e50;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-weight: bold;
    font-size: 20px;
    border: 2px solid #f39c12;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.user-text {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 16px;
    text-shadow: 0 1px 3px rgba(0,0,0,0.4);
}

.user-role {
    margin-left: 5px;
    font-size: 15px;
    font-weight: bold;
}

/* Navegación */
nav {
    background-color: #34495e;
    padding: 10px 0;
}

nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
}

nav ul li {
    margin: 0 10px;
}

nav ul li a {
    color: white;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 3px;
    transition: background-color 0.3s;
}

nav ul li a:hover {
    background-color: #3498db;
}

/* Tarjetas para la página principal */
.card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
}

.card {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 20px;
    width: 300px;
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.card h3 {
    color: #2c3e50;
    margin-top: 0;
}

.card p {
    color: #7f8c8d;
}

.card .btn {
    display: inline-block;
    background-color: #3498db;
    color: white;
    padding: 8px 15px;
    border-radius: 3px;
    text-decoration: none;
    margin-top: 10px;
    transition: background-color 0.3s;
}

.card .btn:hover {
    background-color: #2980b9;
}

/* Tablas */
table {
    border-collapse: collapse;
    width: 100%;
    background-color: #fff;
    box-shadow: 0 0 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

th, td {
    padding: 10px;
    border: 1px solid #ccc;
    text-align: left;
    font-size: 13px;
}

th {
    background-color: #3498db;
    color: white;
}

tr:nth-child(even) {
    background-color: #f2f2f2;
}

/* Botones */
.btn {
    display: inline-block;
    background-color: #3498db;
    color: white;
    padding: 8px 15px;
    border-radius: 3px;
    text-decoration: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #2980b9;
}

.btn-success {
    background-color: #2ecc71;
}

.btn-success:hover {
    background-color: #27ae60;
}

.btn-warning {
    background-color: #f39c12;
}

.btn-warning:hover {
    background-color: #d35400;
}

.btn-danger {
    background-color: #e74c3c;
}

.btn-danger:hover {
    background-color: #c0392b;
}

/* Mensajes */
.error {
    color: #e74c3c;
    font-weight: bold;
    padding: 10px;
    background-color: #fadbd8;
    border-radius: 3px;
    margin-bottom: 20px;
}

.success {
    color: #2ecc71;
    font-weight: bold;
    padding: 10px;
    background-color: #d5f5e3;
    border-radius: 3px;
    margin-bottom: 20px;
}

/* Footer */
footer {
    background-color: #2c3e50;
    color: white;
    text-align: center;
    padding: 20px 0;
    margin-top: 50px;
}
