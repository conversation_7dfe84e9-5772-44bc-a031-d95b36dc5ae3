<?php
/**
 * Consulta de Imponible Hidráulica
 *
 * Este archivo contiene la consulta para el reporte de Imponible Hidráulica.
 */

// Verificar si el usuario está autenticado y tiene permiso
if (!isLoggedIn() || (!isAdmin() && !tienePermiso('ver_reporte_hidraulica'))) {
    echo "<p class='error'>No tienes permiso para acceder a este reporte.</p>";
    return;
}

try {
    $pdo = getConexion();

    // Crear tabla temporal para almacenar los resultados
    $pdo->exec("
        BEGIN
            EXECUTE IMMEDIATE 'DROP TABLE TEMP_HIDRAULICA';
        EXCEPTION
            WHEN OTHERS THEN
                IF SQLCODE != -942 THEN
                    RAISE;
                END IF;
        END;
    ");

    $pdo->exec("
        CREATE GLOBAL TEMPORARY TABLE TEMP_HIDRAULICA (
            LEGAJO VARCHAR2(10),
            NOMBRE VARCHAR2(100),
            CATEGORIA VARCHAR2(50),
            IMPORTE_BASICO NUMBER(10,2),
            IMPORTE_ADICIONAL NUMBER(10,2),
            IMPORTE_TOTAL NUMBER(10,2),
            FECHA DATE
        ) ON COMMIT PRESERVE ROWS
    ");

    // Insertar datos de ejemplo en la tabla temporal
    $pdo->exec("
        INSERT INTO TEMP_HIDRAULICA (LEGAJO, NOMBRE, CATEGORIA, IMPORTE_BASICO, IMPORTE_ADICIONAL, IMPORTE_TOTAL, FECHA)
        VALUES ('3001', 'EDUARDO RAMIREZ', 'A', 8000.00, 1500.00, 9500.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    $pdo->exec("
        INSERT INTO TEMP_HIDRAULICA (LEGAJO, NOMBRE, CATEGORIA, IMPORTE_BASICO, IMPORTE_ADICIONAL, IMPORTE_TOTAL, FECHA)
        VALUES ('3002', 'SILVIA MORALES', 'B', 7500.00, 1200.00, 8700.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    $pdo->exec("
        INSERT INTO TEMP_HIDRAULICA (LEGAJO, NOMBRE, CATEGORIA, IMPORTE_BASICO, IMPORTE_ADICIONAL, IMPORTE_TOTAL, FECHA)
        VALUES ('3003', 'GUSTAVO HERRERA', 'A', 8200.00, 1600.00, 9800.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    $pdo->exec("
        INSERT INTO TEMP_HIDRAULICA (LEGAJO, NOMBRE, CATEGORIA, IMPORTE_BASICO, IMPORTE_ADICIONAL, IMPORTE_TOTAL, FECHA)
        VALUES ('3004', 'MONICA ACOSTA', 'C', 7000.00, 1000.00, 8000.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    $pdo->exec("
        INSERT INTO TEMP_HIDRAULICA (LEGAJO, NOMBRE, CATEGORIA, IMPORTE_BASICO, IMPORTE_ADICIONAL, IMPORTE_TOTAL, FECHA)
        VALUES ('3005', 'RICARDO MENDEZ', 'B', 7600.00, 1300.00, 8900.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    // Ejecutar la consulta final
    $sql = "SELECT LEGAJO, NOMBRE, CATEGORIA, IMPORTE_BASICO, IMPORTE_ADICIONAL, IMPORTE_TOTAL, TO_CHAR(FECHA, 'DD/MM/YYYY') AS FECHA FROM TEMP_HIDRAULICA ORDER BY CATEGORIA, LEGAJO";
    $stmt = $pdo->query($sql);

    // Si estamos en modo de exportación a Excel, no mostrar nada en pantalla
    if (basename($_SERVER['PHP_SELF']) === 'exportar_excel.php') {
        // No hacer nada, solo devolver $stmt para que exportar_excel.php lo use
        return;
    }

    // Si estamos en modo de visualización normal, mostrar los resultados en pantalla
    echo "<h3>Resultados de Imponible Hidráulica</h3>";
    echo "<p>Este reporte muestra los datos de imponible hidráulica.</p>";

} catch (PDOException $e) {
    echo "<p class='error'>Error en la consulta: " . $e->getMessage() . "</p>";
}
?>
