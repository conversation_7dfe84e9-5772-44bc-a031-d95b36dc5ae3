# sqlnet.ora Network Configuration File

# This file is used by the Oracle Net Services to configure the
# client and server networking components.

# NAMES.DIRECTORY_PATH specifies the order of name resolution methods
NAMES.DIRECTORY_PATH= (TNSNAMES, EZCONNECT)

# SQLNET.EXPIRE_TIME specifies the time interval in minutes to send a probe to verify that client/server connections are active
SQLNET.EXPIRE_TIME = 10

# TRACE_LEVEL_CLIENT specifies the level of detail the trace facility records for the client
TRACE_LEVEL_CLIENT = OFF

# TRACE_DIRECTORY_CLIENT specifies the directory for the client trace file
TRACE_DIRECTORY_CLIENT = /tmp

# TRACE_FILE_CLIENT specifies the name of the client trace file
TRACE_FILE_CLIENT = cli_trace

# TRACE_UNIQUE_CLIENT specifies whether a unique trace file is created for each client
TRACE_UNIQUE_CLIENT = ON
