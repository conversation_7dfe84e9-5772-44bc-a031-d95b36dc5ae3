<?php
// Incluir archivo de autenticación
require_once 'auth.php';

// Establecer el modo de visualización de errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Crear Usuario de Prueba</h1>";

// Procesar formulario
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $nombre = $_POST['nombre'] ?? '';
    $email = $_POST['email'] ?? '';
    $rol = $_POST['rol'] ?? 'usuario';
    
    if (empty($username) || empty($password) || empty($nombre)) {
        echo "<p style='color: red; font-weight: bold;'>Por favor, complete todos los campos obligatorios</p>";
    } else {
        try {
            // Obtener conexión a la base de datos
            $pdo = getConexion();
            
            // Verificar si el usuario ya existe
            $sql = "SELECT COUNT(*) FROM usuarios WHERE username = :username";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':username', $username);
            $stmt->execute();
            
            if ($stmt->fetchColumn() > 0) {
                echo "<p style='color: red; font-weight: bold;'>El usuario '$username' ya existe!</p>";
            } else {
                // Encriptar la contraseña
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                
                // Insertar el nuevo usuario
                $sql = "INSERT INTO usuarios (username, password, nombre, email, rol) VALUES (:username, :password, :nombre, :email, :rol)";
                $stmt = $pdo->prepare($sql);
                $stmt->bindParam(':username', $username);
                $stmt->bindParam(':password', $hashedPassword);
                $stmt->bindParam(':nombre', $nombre);
                $stmt->bindParam(':email', $email);
                $stmt->bindParam(':rol', $rol);
                
                if ($stmt->execute()) {
                    echo "<p style='color: green; font-weight: bold;'>Usuario creado correctamente!</p>";
                    echo "<p>Usuario: $username</p>";
                    echo "<p>Contraseña: $password</p>";
                    echo "<p>Rol: $rol</p>";
                    echo "<p><a href='login.php'>Ir a la página de inicio de sesión</a></p>";
                } else {
                    echo "<p style='color: red; font-weight: bold;'>Error al crear el usuario</p>";
                }
            }
        } catch (PDOException $e) {
            echo "<p style='color: red; font-weight: bold;'>Error de conexión a la base de datos!</p>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            
            // Verificar si la tabla de usuarios existe
            if (strpos($e->getMessage(), 'ORA-00942') !== false) {
                echo "<p>La tabla de usuarios no existe. Debes crearla primero.</p>";
                echo "<p><a href='verificar_usuarios.php'>Ir a verificar usuarios</a></p>";
            }
        }
    }
}

// Volver al formulario de prueba
echo "<p><a href='probar_autenticacion.php'>Volver al formulario de prueba</a></p>";
?>
