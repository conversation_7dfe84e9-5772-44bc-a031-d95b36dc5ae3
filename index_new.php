<?php
// Incluir archivo de permisos
require_once 'permisos.php';

// Verificar que el usuario esté autenticado
requireLogin();

// Mostrar mensaje de error de permisos si existe
$errorPermiso = '';
if (isset($_SESSION['error_permiso'])) {
    $errorPermiso = $_SESSION['error_permiso'];
    unset($_SESSION['error_permiso']);
}

// Verificar si es la primera vez que el usuario inicia sesión en esta sesión
$mostrarBienvenida = false;
if (!isset($_SESSION['bienvenida_mostrada'])) {
    $mostrarBienvenida = true;
    $_SESSION['bienvenida_mostrada'] = true;
}

// Obtener el nombre de usuario
$nombreUsuario = isset($_SESSION['user_nombre']) ? $_SESSION['user_nombre'] : $_SESSION['user_username'];

// Obtener la fecha actual en español
setlocale(LC_TIME, 'es_ES.UTF-8', 'es_ES', 'esp');
$dia = date('d');
$mes = date('m');
$anio = date('Y');

// Nombres de los meses en español
$meses = [
    '01' => 'Enero',
    '02' => 'Febrero',
    '03' => 'Marzo',
    '04' => 'Abril',
    '05' => 'Mayo',
    '06' => 'Junio',
    '07' => 'Julio',
    '08' => 'Agosto',
    '09' => 'Septiembre',
    '10' => 'Octubre',
    '11' => 'Noviembre',
    '12' => 'Diciembre'
];

$nombreMes = $meses[$mes];

// Obtener los reportes asignados al usuario
$reportesAsignados = [];
$userId = $_SESSION['user_id'];
$username = $_SESSION['user_username'];
$rol = $_SESSION['user_rol'];

// Cargar la configuración de consultas
require_once 'consultas_sql.php';

// Si el usuario es administrador, mostrar todos los reportes
if ($rol === 'admin') {
    $reportesAsignados = $consultas;
} else {
    // Obtener permisos del usuario desde la base de datos
    try {
        $pdo = getConexion();
        $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id_usuario', $userId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $permisos = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];

            if (!empty($permisos)) {
                $permisosArray = explode(',', $permisos);
                $permisosArray = array_map('trim', $permisosArray);

                // Verificar cada consulta
                foreach ($consultas as $key => $info) {
                    // Si tiene el permiso específico o el permiso general, agregar a reportes asignados
                    if (in_array($info['permiso'], $permisosArray) || in_array('ver_reportes', $permisosArray)) {
                        $reportesAsignados[$key] = $info;
                    }
                }
            }
        }
    } catch (PDOException $e) {
        error_log("Error al obtener permisos para mostrar reportes en index: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <?php include 'head_common.php'; ?>
    <title>Sistema de Reportes de Sueldos</title>
    <style>
        /* Estilos para el modal de bienvenida */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 30px;
            border: 1px solid #888;
            width: 50%;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            animation: modalFadeIn 0.5s;
            text-align: center;
        }

        .modal-content h2 {
            color: #2980b9;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 24px;
        }

        @keyframes modalFadeIn {
            from {opacity: 0; transform: translateY(-50px);}
            to {opacity: 1; transform: translateY(0);}
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
        }

        /* Estilos para los reportes asignados */
        .reportes-asignados {
            margin-top: 30px;
        }

        .reportes-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }

        .categoria-card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            flex: 1;
            min-width: 300px;
        }

        .categoria-titulo {
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-top: 0;
        }

        .reportes-lista {
            list-style-type: none;
            padding: 0;
        }

        .reporte-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        .reporte-link {
            text-decoration: none;
            color: #2980b9;
            font-weight: bold;
            display: block;
        }

        .reporte-descripcion {
            margin: 5px 0 0 0;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
<?php include 'header_with_user.php'; ?>

    <?php include 'nav_menu.php'; ?>

    <main class="container">
        <?php if ($mostrarBienvenida): ?>
        <div id="bienvenida-modal" class="modal" style="display: block;">
            <div class="modal-content">
                <span class="close" onclick="document.getElementById('bienvenida-modal').style.display='none'">&times;</span>
                <h2>¡Bienvenido, <?php echo htmlspecialchars(ucfirst($nombreUsuario)); ?>!</h2>
                <p style="font-size: 18px; text-align: center; margin-bottom: 5px;">
                    Al Sistema de Reportes de Sueldos
                </p>
                <p style="font-size: 16px; text-align: center; color: #667;">
                    <?php echo $dia; ?> de <?php echo $nombreMes; ?> de <?php echo $anio; ?>
                </p>
                <p style="text-align: center; margin-top: 25px;">
                    <button onclick="document.getElementById('bienvenida-modal').style.display='none'" class="btn" style="background-color: #2980b9; color: white; padding: 10px 25px; font-size: 16px; border: none; border-radius: 5px; cursor: pointer; transition: background-color 0.3s;">Continuar</button>
                </p>
            </div>
        </div>
        <?php endif; ?>

        <h2>Bienvenido al Sistema de Reportes de Sueldos</h2>
        <p>Este sistema le permite acceder a diferentes reportes.</p>

        <?php if (!empty($errorPermiso)): ?>
            <div class="error"><?php echo $errorPermiso; ?></div>
        <?php endif; ?>

        <div class="card-container">
            <?php if (tienePermiso('ver_reportes') || isAdmin()): ?>
            <div class="card">
                <h3>Reportes</h3>
                <p>Accede a todos los reportes disponibles.</p>
                <a href="reportes.php" class="btn">Ver todos los reportes</a>
            </div>
            <?php endif; ?>

            <?php if (isAdmin()): ?>
            <div class="card">
                <h3>Administración</h3>
                <p>Gestiona usuarios y permisos del sistema.</p>
                <div style="display: flex; gap: 10px;">
                    <a href="admin_usuarios.php" class="btn">Usuarios</a>
                    <a href="admin_permisos_usuario.php" class="btn">Permisos</a>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <?php if (!empty($reportesAsignados) && $rol === 'usuario'): ?>
        <div class="reportes-asignados">
            <h2>Tus Reportes Asignados</h2>
            <p>Aquí puedes acceder directamente a los reportes que tienes asignados:</p>

            <div class="reportes-container">
                <?php
                // Agrupar reportes por categoría
                $reportesPorCategoria = [];
                foreach ($reportesAsignados as $key => $info) {
                    $categoria = isset($info['categoria']) ? $info['categoria'] : 'Sin categoría';
                    if (!isset($reportesPorCategoria[$categoria])) {
                        $reportesPorCategoria[$categoria] = [];
                    }
                    $reportesPorCategoria[$categoria][$key] = $info;
                }

                // Mostrar reportes por categoría
                foreach ($reportesPorCategoria as $categoria => $reportes):
                ?>
                <div class="categoria-card">
                    <h3 class="categoria-titulo"><?php echo htmlspecialchars($categoria); ?></h3>
                    <ul class="reportes-lista">
                        <?php foreach ($reportes as $key => $info): ?>
                        <li class="reporte-item">
                            <a href="reportes.php?consulta=<?php echo $key; ?>" class="reporte-link">
                                <?php echo htmlspecialchars($info['titulo']); ?>
                            </a>
                            <?php if (isset($info['descripcion'])): ?>
                            <p class="reporte-descripcion"><?php echo htmlspecialchars($info['descripcion']); ?></p>
                            <?php endif; ?>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </main>
</body>
</html>
