<?php
/**
 * Script para corregir los permisos de todos los usuarios
 * 
 * Este script verifica y corrige los permisos de todos los usuarios,
 * asegurándose de que tengan los permisos correctos según su rol y
 * asignaciones específicas.
 */

// Incluir archivos necesarios
require_once 'conexion.php';
require_once 'permisos.php';
require_once 'auth.php';

// Verificar que el usuario esté autenticado y sea administrador
requireLogin();
if ($_SESSION['user_rol'] !== 'admin') {
    $_SESSION['error_permiso'] = 'No tienes permiso para acceder a esta página';
    header('Location: index.php');
    exit;
}

// Mensaje de éxito o error
$mensaje = '';
$error = '';

// Procesar el formulario
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['corregir_permisos'])) {
    try {
        $pdo = getConexion();
        
        // Obtener todos los usuarios
        $sql = "SELECT id, username, rol FROM usuarios";
        $stmt = $pdo->query($sql);
        $usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $usuariosCorregidos = 0;
        
        foreach ($usuarios as $usuario) {
            $userId = $usuario['id'];
            $username = isset($usuario['USERNAME']) ? $usuario['USERNAME'] : $usuario['username'];
            $rol = isset($usuario['ROL']) ? $usuario['ROL'] : $usuario['rol'];
            
            // Obtener permisos actuales
            $permisosActuales = getPermisosUsuario($userId, true);
            
            // Permisos que debe tener según su rol
            $permisosRol = getPermisosRol($rol);
            
            // Permisos adicionales según el usuario
            $permisosAdicionales = [];
            
            // Caso especial para dpohidraulica
            if ($username === 'dpohidraulica') {
                $permisosAdicionales[] = 'ver_reporte_hidraulica';
                $permisosAdicionales[] = 'exportar_excel';
            }
            
            // Combinar permisos
            $permisosCombinados = array_unique(array_merge($permisosRol, $permisosAdicionales));
            
            // Verificar si hay diferencias
            $diferencias = array_diff($permisosCombinados, $permisosActuales);
            
            if (!empty($diferencias)) {
                // Guardar los permisos corregidos
                $resultado = guardarPermisosUsuario($userId, $permisosCombinados);
                
                if ($resultado === true) {
                    $usuariosCorregidos++;
                } else {
                    $error .= "Error al corregir permisos para $username: $resultado<br>";
                }
            }
        }
        
        if ($usuariosCorregidos > 0) {
            $mensaje = "Se han corregido los permisos de $usuariosCorregidos usuarios.";
        } else {
            $mensaje = "No fue necesario corregir permisos para ningún usuario.";
        }
        
        // Limpiar la caché de permisos
        if (isset($_SESSION['permisos_cache'])) {
            unset($_SESSION['permisos_cache']);
            $mensaje .= " La caché de permisos ha sido limpiada.";
        }
        
    } catch (PDOException $e) {
        $error = "Error en la base de datos: " . $e->getMessage();
    }
}

// Obtener todos los usuarios para mostrar en la página
try {
    $pdo = getConexion();
    $sql = "SELECT id, username, nombre, rol FROM usuarios ORDER BY rol, username";
    $stmt = $pdo->query($sql);
    $usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error al obtener usuarios: " . $e->getMessage();
    $usuarios = [];
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Corregir Permisos - Sistema de Reportes de Sueldos</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
<?php include 'header_with_user.php'; ?>

    <nav>
        <div class="container">
            <ul>
                <li><a href="index.php">Inicio</a></li>
                <li><a href="reportes.php">Reportes</a></li>
                <?php if (tienePermiso('admin_usuarios')): ?>
                    <li><a href="admin_usuarios.php">Administrar Usuarios</a></li>
                    <li><a href="admin_permisos_usuario.php">Administrar Permisos</a></li>
                    <li><a href="corregir_permisos.php" class="active">Corregir Permisos</a></li>
                <?php endif; ?>
                <li><a href="logout.php">Cerrar Sesión (<?php echo htmlspecialchars($_SESSION['user_username']); ?>)</a></li>
            </ul>
        </div>
    </nav>

    <main class="container">
        <h2>Corregir Permisos de Usuarios</h2>
        
        <?php if (!empty($mensaje)): ?>
            <div class="success"><?php echo $mensaje; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <p>Esta herramienta verifica y corrige los permisos de todos los usuarios según su rol y asignaciones específicas.</p>
        
        <form method="post" style="margin-bottom: 20px;">
            <button type="submit" name="corregir_permisos" class="btn btn-primary">Corregir Permisos de Todos los Usuarios</button>
        </form>
        
        <h3>Usuarios del Sistema</h3>
        
        <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Usuario</th>
                    <th>Nombre</th>
                    <th>Rol</th>
                    <th>Acciones</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($usuarios as $usuario): ?>
                    <tr>
                        <td><?php echo $usuario['id']; ?></td>
                        <td><?php echo htmlspecialchars(isset($usuario['USERNAME']) ? $usuario['USERNAME'] : $usuario['username']); ?></td>
                        <td><?php echo htmlspecialchars(isset($usuario['NOMBRE']) ? $usuario['NOMBRE'] : $usuario['nombre']); ?></td>
                        <td><?php echo htmlspecialchars(isset($usuario['ROL']) ? $usuario['ROL'] : $usuario['rol']); ?></td>
                        <td>
                            <a href="admin_permisos_usuario.php?user_id=<?php echo $usuario['id']; ?>" class="btn btn-sm btn-info">Ver Permisos</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Sistema de Reportes de Sueldos. Todos los derechos reservados.</p>
        </div>
    </footer>
</body>
</html>
