<?php
// Incluir archivo de permisos
require_once 'permisos.php';

// Verificar que el usuario esté autenticado y tenga permiso
requireLogin();
requirePermiso('admin_usuarios');
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <?php include 'head_common.php'; ?>
    <title>Herramientas de Diagnóstico - Sistema de Reportes de Sueldos</title>
    <style>
        .herramientas-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }

        .herramienta-card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            flex: 1;
            min-width: 300px;
        }

        .herramienta-titulo {
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-top: 0;
        }

        .herramienta-descripcion {
            margin-bottom: 20px;
        }

        .btn-herramienta {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 10px;
            transition: background-color 0.3s;
        }

        .btn-herramienta:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
<?php include 'header_with_user.php'; ?>

    <?php include 'nav_menu.php'; ?>

    <main class="container">
        <h2>Herramientas de Diagnóstico</h2>

        <p>Estas herramientas te ayudarán a diagnosticar y corregir problemas en el sistema.</p>

        <div class="herramientas-container">
            <div class="herramienta-card">
                <h3 class="herramienta-titulo">Verificación de Usuarios</h3>
                <p class="herramienta-descripcion">Verifica los usuarios existentes en el sistema y permite crear el usuario dpohidraulica si no existe.</p>
                <a href="verificar_usuarios_existentes.php" class="btn-herramienta">Verificar Usuarios</a>
            </div>

            <div class="herramienta-card">
                <h3 class="herramienta-titulo">Corrección de Permisos</h3>
                <p class="herramienta-descripcion">Corrige los permisos del usuario dpohidraulica para que pueda acceder a los reportes de Hidráulica.</p>
                <a href="asignar_permisos_dpohidraulica.php" class="btn-herramienta">Asignar Permisos dpohidraulica</a>
            </div>

            <div class="herramienta-card">
                <h3 class="herramienta-titulo">Limpieza de Caché</h3>
                <p class="herramienta-descripcion">Limpia la caché de permisos y reinicia la sesión para aplicar los cambios.</p>
                <a href="limpiar_sesion.php" class="btn-herramienta">Limpiar Caché y Reiniciar Sesión</a>
            </div>

            <div class="herramienta-card">
                <h3 class="herramienta-titulo">Corrección de Permisos para Todos los Usuarios</h3>
                <p class="herramienta-descripcion">Corrige los permisos de todos los usuarios del sistema.</p>
                <a href="corregir_permisos.php" class="btn-herramienta">Corregir Permisos de Todos los Usuarios</a>
            </div>

            <div class="herramienta-card">
                <h3 class="herramienta-titulo">Modificar Permisos del Usuario ID 2</h3>
                <p class="herramienta-descripcion">Modifica los permisos del usuario con ID 2, eliminando permisos innecesarios.</p>
                <a href="modificar_permisos_usuario2.php" class="btn-herramienta">Modificar Permisos Usuario ID 2</a>
            </div>
        </div>
    </main>
</body>
</html>
