<?php
// Incluir archivo de permisos
require_once 'permisos.php';

// Verificar que el usuario esté autenticado y sea administrador
requireLogin();
requireAdmin();

$mensaje = '';
$error = '';

// Procesar el formulario
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Esta es una implementación básica
    // En una implementación real, guardarías los permisos en la base de datos
    $mensaje = 'Los permisos han sido actualizados correctamente';
}

// Obtener todos los permisos disponibles
$permisos = getPermisos();

// Obtener los permisos asignados a cada rol
$permisosRoles = getPermisosRoles();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administración de Permisos - Sistema de Consultas Oracle</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .permisos-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        
        .rol-card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            flex: 1;
            min-width: 300px;
        }
        
        .rol-card h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .permiso-item {
            margin-bottom: 10px;
        }
        
        .permiso-item label {
            display: flex;
            align-items: center;
        }
        
        .permiso-item input[type="checkbox"] {
            margin-right: 10px;
        }
        
        .permiso-descripcion {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-left: 25px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>Sistema de Consultas Oracle</h1>
        </div>
    </header>
    
    <nav>
        <div class="container">
            <ul>
                <li><a href="index.php">Inicio</a></li>
                <li><a href="consulta.php">Imponible Hidráulica</a></li>
                <?php if (isAdmin()): ?>
                    <li><a href="admin_usuarios.php">Administrar Usuarios</a></li>
                    <li><a href="admin_permisos.php">Administrar Permisos</a></li>
                <?php endif; ?>
                <li><a href="logout.php">Cerrar Sesión (<?php echo htmlspecialchars($_SESSION['user_username']); ?>)</a></li>
            </ul>
        </div>
    </nav>
    
    <main class="container">
        <h2>Administración de Permisos</h2>
        
        <?php if (!empty($mensaje)): ?>
            <div class="success"><?php echo $mensaje; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <p>En esta sección puedes configurar los permisos asignados a cada rol del sistema.</p>
        
        <div class="permisos-container">
            <?php foreach ($permisosRoles as $rol => $permisosRol): ?>
                <div class="rol-card">
                    <h3><?php echo ucfirst($rol); ?></h3>
                    <form method="post">
                        <input type="hidden" name="rol" value="<?php echo $rol; ?>">
                        
                        <?php foreach ($permisos as $codigo => $descripcion): ?>
                            <div class="permiso-item">
                                <label>
                                    <input type="checkbox" name="permisos[]" value="<?php echo $codigo; ?>" 
                                        <?php echo in_array($codigo, $permisosRol) ? 'checked' : ''; ?>>
                                    <?php echo $codigo; ?>
                                </label>
                                <div class="permiso-descripcion"><?php echo $descripcion; ?></div>
                            </div>
                        <?php endforeach; ?>
                        
                        <button type="submit" class="btn btn-success" style="margin-top: 15px;">Guardar Cambios</button>
                    </form>
                </div>
            <?php endforeach; ?>
        </div>
    </main>
    
    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Sistema de Consultas Oracle. Todos los derechos reservados.</p>
        </div>
    </footer>
</body>
</html>
