<?php
// Incluir archivo de conexión
require_once 'conexion.php';

try {
    $pdo = getConexion();
    
    // Crear tabla de permisos de usuario si no existe
    $sql = "
    BEGIN
        EXECUTE IMMEDIATE 'DROP TABLE permisos_usuario';
        EXCEPTION WHEN OTHERS THEN NULL;
    END;
    ";
    $pdo->exec($sql);
    
    // Especificar el tablespace a utilizar
    $tablespace = "SUELDOS27"; // Tablespace especificado por el usuario
    
    // Crear tabla de permisos de usuario
    $sql = "
    CREATE TABLE permisos_usuario (
        id NUMBER PRIMARY KEY,
        id_usuario NUMBER NOT NULL,
        permisos VARCHAR2(1000) NOT NULL,
        fecha_creacion DATE DEFAULT SYSDATE,
        CONSTRAINT fk_permisos_usuario FOREIGN KEY (id_usuario) REFERENCES usuarios(id)
    ) TABLESPACE " . $tablespace;
    $pdo->exec($sql);
    
    // Crear secuencia para el ID
    $sql = "
    BEGIN
        EXECUTE IMMEDIATE 'DROP SEQUENCE permisos_usuario_seq';
        EXCEPTION WHEN OTHERS THEN NULL;
    END;
    ";
    $pdo->exec($sql);
    
    $sql = "CREATE SEQUENCE permisos_usuario_seq START WITH 1 INCREMENT BY 1";
    $pdo->exec($sql);
    
    // Crear trigger para autoincrement
    $sql = "
    BEGIN
        EXECUTE IMMEDIATE 'DROP TRIGGER permisos_usuario_trg';
        EXCEPTION WHEN OTHERS THEN NULL;
    END;
    ";
    $pdo->exec($sql);
    
    $sql = "
    CREATE OR REPLACE TRIGGER permisos_usuario_trg
    BEFORE INSERT ON permisos_usuario
    FOR EACH ROW
    BEGIN
        SELECT permisos_usuario_seq.NEXTVAL INTO :new.id FROM dual;
    END;
    ";
    $pdo->exec($sql);
    
    echo "Tabla de permisos de usuario creada correctamente.";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
