<?php
// Incluir archivos necesarios
require_once 'conexion.php';
require_once 'permisos.php';
require_once 'auth.php';
require_once 'asignar_permiso_hidraulica_silencioso.php';
require_once 'config_logs.php';

// Verificar si el usuario está autenticado
requireLogin();

// Asignar permiso de Hidráulica silenciosamente si el usuario no es admin
if ($_SESSION['user_rol'] !== 'admin') {
    asignarPermisoHidraulicaSilencioso($_SESSION['user_id']);
}

// Obtener la consulta seleccionada
$consulta = isset($_GET['consulta']) ? $_GET['consulta'] : '';

// Obtener el período seleccionado (mes y año)
$mesSeleccionado = isset($_GET['mes']) ? $_GET['mes'] : date('m');
$anioSeleccionado = isset($_GET['anio']) ? $_GET['anio'] : date('y');

// Validar mes (01-12)
if (!preg_match('/^(0[1-9]|1[0-2])$/', $mesSeleccionado)) {
    $mesSeleccionado = date('m');
}

// Validar año (00-99)
if (!preg_match('/^[0-9]{2}$/', $anioSeleccionado)) {
    $anioSeleccionado = date('y');
}

// Crear el sufijo de la tabla (MMAA)
$sufijo_tabla = $mesSeleccionado . $anioSeleccionado;

// Cargar las definiciones de consultas
require_once 'consultas_sql.php';

// Verificar si el usuario tiene permiso para ver al menos una consulta
$tieneAlgunPermiso = false;
$userId = $_SESSION['user_id'];
$username = $_SESSION['user_username'];
$rol = $_SESSION['user_rol'];

// Si el usuario es administrador, tiene permiso para ver todas las consultas
if ($rol === 'admin') {
    $tieneAlgunPermiso = true;
    writeLog("Usuario administrador, tiene permiso para ver todas las consultas", 'INFO', 'REPORTES');
} else {
    // Obtener permisos directamente de la base de datos
    try {
        $pdo = getConexion();
        $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id_usuario', $userId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $permisos = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];

            if (!empty($permisos)) {
                $permisosArray = explode(',', $permisos);
                $permisosArray = array_map('trim', $permisosArray);

                // Verificar si tiene permiso general para ver reportes
                if (in_array('ver_reportes', $permisosArray)) {
                    $tieneAlgunPermiso = true;
                    error_log("Usuario tiene permiso general para ver reportes");
                } else {
                    // Verificar si tiene permiso para ver alguna consulta específica
                    foreach ($consultas as $key => $info) {
                        if (in_array($info['permiso'], $permisosArray)) {
                            $tieneAlgunPermiso = true;
                            error_log("Usuario tiene permiso para ver la consulta $key");
                            break;
                        }
                    }
                }
            }
        }
    } catch (PDOException $e) {
        error_log("Error al verificar permisos en la base de datos: " . $e->getMessage());
    }

    // Si no se encontraron permisos en la base de datos, verificar con la función tienePermiso
    if (!$tieneAlgunPermiso) {
        // Verificar si el usuario tiene el permiso general para ver reportes
        if (tienePermiso('ver_reportes', true)) {
            $tieneAlgunPermiso = true;
            error_log("Usuario tiene permiso general para ver reportes (verificado con tienePermiso)");
        } else {
            // Verificar si el usuario tiene permiso para ver alguna consulta específica
            foreach ($consultas as $key => $info) {
                error_log("Verificando permiso para consulta $key: " . $info['permiso']);
                if (tienePermiso($info['permiso'], true)) {
                    $tieneAlgunPermiso = true;
                    error_log("Usuario tiene permiso para ver la consulta $key (verificado con tienePermiso)");
                    break;
                }
            }
        }
    }
}

error_log("Usuario " . $_SESSION['user_username'] . " tiene algún permiso: " . ($tieneAlgunPermiso ? 'SI' : 'NO'));

if (!$tieneAlgunPermiso) {
    // Obtener información detallada sobre los permisos del usuario
    $permisosUsuario = getPermisosUsuario($userId, true);

    $mensajeError = 'No tienes permiso para acceder a ninguna consulta. ';
    $mensajeError .= 'Usuario: ' . $username . ', ';
    $mensajeError .= 'Permisos asignados: ' . (empty($permisosUsuario) ? 'Ninguno' : implode(', ', $permisosUsuario));

    $_SESSION['error_permiso'] = $mensajeError;
    error_log("Redirigiendo a index.php por falta de permisos: $mensajeError");
    header('Location: index.php');
    exit;
}

// Mensaje de error
$error = '';
if (isset($_SESSION['error_permiso'])) {
    $error = $_SESSION['error_permiso'];
    unset($_SESSION['error_permiso']);
} elseif (isset($_SESSION['error_exportar'])) {
    $error = $_SESSION['error_exportar'];
    unset($_SESSION['error_exportar']);
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reportes - Sistema de Reportes de Sueldos</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .reportes-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }

        .reportes-menu {
            flex: 1;
            min-width: 250px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }

        .reporte-content {
            flex: 3;
            min-width: 600px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }

        .reportes-menu h3, .reporte-content h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .categoria-menu {
            margin-bottom: 20px;
        }

        .categoria-menu h4 {
            background-color: #34495e;
            color: white;
            padding: 10px;
            margin: 0;
            border-radius: 5px 5px 0 0;
            font-size: 16px;
        }

        .categoria-items {
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }

        .reporte-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .reporte-item:last-child {
            border-bottom: none;
        }

        .reporte-item:hover {
            background-color: #f5f5f5;
        }

        .reporte-item.active {
            background-color: #3498db;
            color: white;
        }

        .reporte-item a {
            display: block;
            text-decoration: none;
            color: inherit;
        }

        .no-reporte-selected {
            color: #7f8c8d;
            text-align: center;
            padding: 20px;
        }

        .reporte-actions {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }

        /* Estilos para las pestañas */
        .tabs {
            width: 100%;
        }

        .tab-header {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 15px;
        }

        .tab-button {
            padding: 10px 15px;
            cursor: pointer;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .tab-button:hover {
            background-color: #e9ecef;
        }

        .tab-button.active {
            background-color: #3498db;
            color: white;
            border-color: #3498db;
        }

        .tab-content {
            padding: 10px 0;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }
    </style>
</head>
<body>
<?php include 'header_with_user.php'; ?>

    <nav>
        <div class="container">
            <ul>
                <li><a href="index.php">Inicio</a></li>
                <?php
                // Verificar si el usuario tiene permiso para ver al menos una consulta
                $mostrarReportes = tienePermiso('ver_reportes');
                if (!$mostrarReportes) {
                    // Verificar permisos específicos para consultas
                    if (tienePermiso('ver_consulta_hidraulica') || tienePermiso('ver_consulta_policia')) {
                        $mostrarReportes = true;
                    }
                }
                if ($mostrarReportes):
                ?>
                    <li><a href="reportes.php">Reportes</a></li>
                <?php endif; ?>
                <?php if (tienePermiso('admin_usuarios')): ?>
                    <li><a href="admin_usuarios.php">Administrar Usuarios</a></li>
                    <li><a href="admin_permisos_usuario.php">Administrar Permisos</a></li>
                    <li><a href="corregir_permisos.php">Corregir Permisos</a></li>
                    <li><a href="ver_logs.php">Ver Logs</a></li>
                <?php endif; ?>
                <li><a href="logout.php">Cerrar Sesión (<?php echo htmlspecialchars($_SESSION['user_username']); ?>)</a></li>
            </ul>
        </div>
    </nav>

    <main class="container">
        <h2>Reportes</h2>

        <?php if (!empty($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>

        <p>En esta sección puedes acceder los reportes asignados.</p>

        <!-- Selector de Período -->
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; border: 1px solid #dee2e6;">
            <h3 style="margin-top: 0; color: #495057;">Seleccionar Período</h3>
            <form method="get" style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
                <div style="display: flex; align-items: center; gap: 5px;">
                    <label for="mes" style="font-weight: bold; color: #495057;">Mes:</label>
                    <select name="mes" id="mes" style="padding: 8px; border: 1px solid #ced4da; border-radius: 4px; background-color: white;">
                        <option value="01" <?php echo $mesSeleccionado == '01' ? 'selected' : ''; ?>>01 - Enero</option>
                        <option value="02" <?php echo $mesSeleccionado == '02' ? 'selected' : ''; ?>>02 - Febrero</option>
                        <option value="03" <?php echo $mesSeleccionado == '03' ? 'selected' : ''; ?>>03 - Marzo</option>
                        <option value="04" <?php echo $mesSeleccionado == '04' ? 'selected' : ''; ?>>04 - Abril</option>
                        <option value="05" <?php echo $mesSeleccionado == '05' ? 'selected' : ''; ?>>05 - Mayo</option>
                        <option value="06" <?php echo $mesSeleccionado == '06' ? 'selected' : ''; ?>>06 - Junio</option>
                        <option value="07" <?php echo $mesSeleccionado == '07' ? 'selected' : ''; ?>>07 - Julio</option>
                        <option value="08" <?php echo $mesSeleccionado == '08' ? 'selected' : ''; ?>>08 - Agosto</option>
                        <option value="09" <?php echo $mesSeleccionado == '09' ? 'selected' : ''; ?>>09 - Septiembre</option>
                        <option value="10" <?php echo $mesSeleccionado == '10' ? 'selected' : ''; ?>>10 - Octubre</option>
                        <option value="11" <?php echo $mesSeleccionado == '11' ? 'selected' : ''; ?>>11 - Noviembre</option>
                        <option value="12" <?php echo $mesSeleccionado == '12' ? 'selected' : ''; ?>>12 - Diciembre</option>
                    </select>
                </div>

                <div style="display: flex; align-items: center; gap: 5px;">
                    <label for="anio" style="font-weight: bold; color: #495057;">Año:</label>
                    <select name="anio" id="anio" style="padding: 8px; border: 1px solid #ced4da; border-radius: 4px; background-color: white;">
                        <?php
                        // Generar opciones de años (desde 2020 hasta año actual + 2)
                        $anioActual = date('Y');
                        for ($i = 2020; $i <= $anioActual + 2; $i++) {
                            $anio2Digitos = substr($i, -2);
                            $selected = ($anioSeleccionado == $anio2Digitos) ? 'selected' : '';
                            echo "<option value='$anio2Digitos' $selected>$anio2Digitos - $i</option>";
                        }
                        ?>
                    </select>
                </div>

                <button type="submit" style="padding: 8px 16px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">
                    Aplicar Período
                </button>

                <div style="margin-left: 20px; padding: 8px 12px; background-color: #e9ecef; border-radius: 4px; font-weight: bold; color: #495057;">
                    Tabla actual: sueldo<?php echo $sufijo_tabla; ?>
                </div>

                <?php if (!empty($consulta)): ?>
                <input type="hidden" name="consulta" value="<?php echo htmlspecialchars($consulta); ?>">
                <?php endif; ?>
            </form>
        </div>

        <div class="reportes-container">
            <div class="reportes-menu">
                <h3>Reportes Disponibles</h3>

                <div class="tabs">
                    <div class="tab-header">
                        <div class="tab-button active" data-tab="sin-ajuste">Sin Ajuste</div>
                        <div class="tab-button" data-tab="con-ajuste">Con Ajuste</div>
                    </div>

                    <div class="tab-content">
                        <div class="tab-pane active" id="sin-ajuste">
                            <div class="categoria-items">
                            <?php
                            // Obtener consultas de la categoría "Sin Ajuste"
                            $consultasCategoria = getConsultasPorCategoria("Sin Ajuste");

                            // Para la pestaña "Sin Ajuste", mostrar solo los reportes específicos (para todos los usuarios)
                            $reportesPermitidosSinAjuste = [
                                'falla_caja',
                                'san_nicolas_mensual',
                                'imponible_poder_judicial',
                                'reparto_poder_judicial',
                                'legislatura_imponible',
                                'legislatura_reparto',
                                'imponible_hidraulica',
                                'policia_imponible',
                                'nivel_central_imponible',
                                'hospital_rawson_imponible',
                                'hospital_marcial_quiroga_imponible',
                                'servicio_penitenciario_imponible_con_recargo',
                                'servicio_penitenciario_imponible_sin_recargo',
                                'servicio_penitenciario_servicio_con_recargo',
                                'servicio_penitenciario_servicio_sin_recargo',
                                'municipalidad_chimbas_mensual',
                                'municipalidad_calingasta_mensual'
                            ];

                            $mostrarReportes = false;
                            foreach ($consultasCategoria as $key => $info) {
                                if (in_array($key, $reportesPermitidosSinAjuste)) {
                                    $mostrarReportes = true;
                            ?>
                                <div class="reporte-item <?php echo $consulta === $key ? 'active' : ''; ?>">
                                    <a href="reportes.php?consulta=<?php echo $key; ?>&mes=<?php echo $mesSeleccionado; ?>&anio=<?php echo $anioSeleccionado; ?>">
                                        <?php echo htmlspecialchars($info['titulo']); ?>
                                    </a>
                                </div>
                            <?php
                                }
                            }

                            // Si no hay reportes para mostrar
                            if (!$mostrarReportes) {
                                echo '<p style="padding: 15px; color: #666; text-align: center; font-style: italic;">No tienes reportes asignados en esta categoría.<br>Contacta al administrador para solicitar acceso.</p>';
                            }
                            ?>
                            </div>
                        </div>
                        <div class="tab-pane" id="con-ajuste">
                            <div class="categoria-items">
                            <?php
                            // Obtener consultas de la categoría "Con Ajuste"
                            $consultasCategoria = getConsultasPorCategoria("Con Ajuste");

                            // Para la pestaña "Con Ajuste", mostrar solo los 4 reportes específicos (para todos los usuarios)
                            $reportesPermitidos = [
                                'obra_social_provincia_regimen_1_21',
                                'direccion_general_rentas_listado_mensual',
                                'obra_social_provincia_completo',
                                'imponible_hidraulica_con_ajuste'
                            ];



                            $mostrarReportes = false;
                            foreach ($consultasCategoria as $key => $info) {
                                if (in_array($key, $reportesPermitidos)) {
                                    $mostrarReportes = true;
                            ?>
                                <div class="reporte-item <?php echo $consulta === $key ? 'active' : ''; ?>">
                                    <a href="reportes.php?consulta=<?php echo $key; ?>&mes=<?php echo $mesSeleccionado; ?>&anio=<?php echo $anioSeleccionado; ?>">
                                        <?php echo htmlspecialchars($info['titulo']); ?>
                                    </a>
                                </div>
                            <?php
                                }
                            }

                            // Si no hay reportes para mostrar
                            if (!$mostrarReportes) {
                                echo '<p style="padding: 15px; color: #666; text-align: center; font-style: italic;">No tienes reportes asignados en esta categoría.<br>Contacta al administrador para solicitar acceso.</p>';
                            }
                            ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="reporte-content">
                <?php
                $puedeVerReporte = false;

                if (!empty($consulta) && isset($consultas[$consulta])) {
                    // Si es administrador, siempre tiene permiso
                    if ($rol === 'admin') {
                        $puedeVerReporte = true;
                    } else {
                        // Para usuarios normales, verificar según el usuario y el reporte
                        if ($_SESSION['user_username'] === 'dpohidraulica') {
                            // El usuario dpohidraulica solo puede ver el reporte de Imponible Hidráulica con ajuste
                            if ($consulta === 'imponible_hidraulica_con_ajuste') {
                                $puedeVerReporte = true;
                            } else {
                                $puedeVerReporte = false;
                            }
                        } else {
                            // Otros usuarios pueden ver los reportes específicos
                            $reportesPermitidosUsuarios = [
                                // Reportes "Con Ajuste"
                                'imponible_hidraulica_con_ajuste',
                                'obra_social_provincia_completo',
                                'obra_social_provincia_regimen_1_21',
                                'direccion_general_rentas_listado_mensual',
                                // Reportes "Sin Ajuste"
                                'falla_caja',
                                'san_nicolas_mensual',
                                'imponible_poder_judicial',
                                'reparto_poder_judicial',
                                'legislatura_imponible',
                                'legislatura_reparto',
                                'imponible_hidraulica',
                                'policia_imponible',
                                'nivel_central_imponible',
                                'hospital_rawson_imponible',
                                'hospital_marcial_quiroga_imponible',
                                'servicio_penitenciario_imponible_con_recargo',
                                'servicio_penitenciario_imponible_sin_recargo',
                                'servicio_penitenciario_servicio_con_recargo',
                                'servicio_penitenciario_servicio_sin_recargo',
                                'municipalidad_chimbas_mensual',
                                'municipalidad_calingasta_mensual'
                            ];

                            if (in_array($consulta, $reportesPermitidosUsuarios)) {
                                $puedeVerReporte = true;
                            } else {
                                // Para otros reportes, verificar permisos
                                $puedeVerReporte = tienePermiso($consultas[$consulta]['permiso'], true);
                            }
                        }
                    }
                }

                if ($puedeVerReporte):
                ?><div class="reporte-contenido"><?php
                    try {
                        // Obtener información de la consulta
                        $tipoConsulta = isset($consultas[$consulta]['tipo']) ? $consultas[$consulta]['tipo'] : 'php';

                        // Ejecutar la consulta según su tipo
                        if ($tipoConsulta === 'sql_directo') {
                            // Ejecutar la consulta SQL directamente
                            $sql = $consultas[$consulta]['sql'];

                            // Reemplazar las referencias a las tablas con el período seleccionado
                            $sql = str_replace('sueldo0425', 'sueldo' . $sufijo_tabla, $sql);
                            $sql = str_replace('ciescu0425', 'ciescu' . $sufijo_tabla, $sql);
                            $sql = str_replace('imponi0425B', 'imponi' . $sufijo_tabla . 'B', $sql);
                            $sql = str_replace('CIMENS0425', 'CIMENS' . $sufijo_tabla, $sql);
                            $sql = str_replace('CIescu0425', 'CIescu' . $sufijo_tabla, $sql);
                            $sql = str_replace('REPAR0425D', 'REPAR' . $sufijo_tabla . 'D', $sql);

                            // Limitar a los primeros 20 registros
                            $sql = "SELECT * FROM (" . $sql . ") WHERE ROWNUM <= 20";

                            // Mostrar título y descripción con el período
                            echo "<h3>" . htmlspecialchars($consultas[$consulta]['titulo']) . " - Período: " . $mesSeleccionado . "/" . $anioSeleccionado . "</h3>";
                            echo "<p>" . htmlspecialchars($consultas[$consulta]['descripcion']) . "</p>";
                            echo "<p><strong>Tabla utilizada:</strong> sueldo" . $sufijo_tabla . " <span style='color: #e74c3c; font-weight: bold;'>(Mostrando primeros 20 registros)</span></p>";

                            // Ejecutar la consulta
                            try {
                                $pdo = getConexion();
                                $stmt = $pdo->query($sql);
                            } catch (PDOException $e) {
                                echo "<p class='error'>Error al ejecutar la consulta SQL: " . $e->getMessage() . "</p>";
                                echo "<p class='error'>Consulta ejecutada: " . htmlspecialchars($sql) . "</p>";
                                $stmt = null;
                            }
                        } else {
                            // Para consultas que requieren archivo
                            $archivoConsulta = $consultas[$consulta]['archivo'];

                            if (!file_exists($archivoConsulta)) {
                                echo "<p class='error'>El archivo de consulta no existe: " . htmlspecialchars($archivoConsulta) . "</p>";
                            } else {
                                if ($tipoConsulta === 'sql') {
                                    // Incluir el archivo para ejecutar scripts SQL
                                    require_once 'ejecutar_script_sql.php';

                                    // Ejecutar el script SQL
                                    $stmt = ejecutarScriptSQL($archivoConsulta);

                                    echo "<h3>" . htmlspecialchars($consultas[$consulta]['titulo']) . " - Período: " . $mesSeleccionado . "/" . $anioSeleccionado . "</h3>";
                                    echo "<p>" . htmlspecialchars($consultas[$consulta]['descripcion']) . "</p>";
                                    echo "<p><strong>Tabla utilizada:</strong> sueldo" . $sufijo_tabla . " <span style='color: #e74c3c; font-weight: bold;'>(Mostrando primeros 20 registros)</span></p>";
                                } else {
                                    // Incluir el archivo PHP directamente
                                    include $archivoConsulta;
                                }
                            }
                        }

                        // Verificar si la variable $stmt está definida
                        if (isset($stmt) && $stmt !== null) {
                            // Obtener los nombres de las columnas
                            $columnNames = [];
                            for ($i = 0; $i < $stmt->columnCount(); $i++) {
                                $meta = $stmt->getColumnMeta($i);
                                $columnNames[] = $meta['name'];
                            }

                            echo '<div style="overflow-x: auto; max-width: 100%;">';
                            echo '<table style="width: 100%; border-collapse: collapse; font-size: 14px;">';
                            echo "<tr>";
                            foreach ($columnNames as $header) {
                                echo '<th style="padding: 8px; text-align: left; border: 1px solid #ddd; background-color: #f2f2f2;">' . htmlspecialchars($header) . '</th>';
                            }
                            echo "</tr>";

                            // Limitar a mostrar solo los primeros 20 registros
                            $contador = 0;
                            $totalRegistros = 0;

                            // Primero contar todos los registros
                            while ($fila = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                $totalRegistros++;
                                if ($contador < 20) {
                                    echo "<tr>";
                                    foreach ($columnNames as $key) {
                                        echo '<td style="padding: 8px; text-align: left; border: 1px solid #ddd;">' . htmlspecialchars($fila[$key]) . '</td>';
                                    }
                                    echo "</tr>";
                                    $contador++;
                                }
                            }

                            echo "</table>";

                            // Mostrar información sobre la limitación
                            if ($totalRegistros > 20) {
                                echo '<div style="margin-top: 15px; padding: 10px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; color: #856404;">';
                                echo '<strong>Nota:</strong> Se muestran los primeros 20 registros de un total de ' . $totalRegistros . ' registros encontrados. ';
                                echo 'Para ver todos los registros, utiliza la función "Exportar a Excel".';
                                echo '</div>';
                            } else {
                                echo '<div style="margin-top: 15px; padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; color: #155724;">';
                                echo '<strong>Total de registros:</strong> ' . $totalRegistros;
                                echo '</div>';
                            }

                            echo "</div>";
                        } else {
                            echo "<p class='error'>No se pudo ejecutar la consulta. La variable \$stmt no está definida.</p>";
                        }

                        echo '<div class="reporte-actions" style="margin-top: 20px; text-align: center;">';
                        echo '<a href="exportar_excel_simple.php?consulta=' . urlencode($consulta) . '&mes=' . urlencode($mesSeleccionado) . '&anio=' . urlencode($anioSeleccionado) . '" class="btn" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; font-size: 14px; font-weight: bold; text-transform: uppercase; border-radius: 4px; text-decoration: none; box-shadow: 0 2px 4px rgba(0,0,0,0.2); transition: all 0.3s ease;">EXPORTAR A EXCEL EN FORMATO CSV</a>';

                        echo '</div>';

                    } catch (PDOException $e) {
                        echo "<p class='error'>Error en la conexión o la consulta: " . $e->getMessage() . "</p>";
                    }
                    ?></div>
                <?php else: ?>
                    <div class="no-reporte-selected">
                        <?php if ($rol === 'admin'): ?>
                            <p>Selecciona un reporte de la lista para ver los resultados</p>
                        <?php else: ?>
                            <p>No tienes acceso a ningún reporte</p>
                            <p style="font-size: 14px; color: #666; margin-top: 10px;">Contacta al administrador para solicitar acceso a los reportes que necesitas.</p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Sistema de Reportes de Sueldos. Todos los derechos reservados.</p>
        </div>
    </footer>

    <script>
        // Código para manejar las pestañas
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabPanes = document.querySelectorAll('.tab-pane');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remover la clase active de todos los botones y paneles
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabPanes.forEach(pane => pane.classList.remove('active'));

                    // Agregar la clase active al botón clickeado
                    this.classList.add('active');

                    // Mostrar el panel correspondiente
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
