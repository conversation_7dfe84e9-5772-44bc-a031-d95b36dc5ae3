<?php
// Incluir archivo de conexión
require_once 'conexion.php';

// Establecer el modo de visualización de errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Verificación de Usuarios Existentes</h1>";

try {
    // Obtener conexión a la base de datos
    $pdo = getConexion();
    
    echo "<p style='color: green; font-weight: bold;'>Conexión a la base de datos exitosa!</p>";
    
    // Verificar si la tabla de usuarios existe
    try {
        $sql = "SELECT * FROM usuarios ORDER BY id";
        $stmt = $pdo->query($sql);
        $usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p style='color: green; font-weight: bold;'>La tabla de usuarios existe!</p>";
        echo "<p>Número de usuarios registrados: " . count($usuarios) . "</p>";
        
        // Mostrar los usuarios registrados
        if (count($usuarios) > 0) {
            echo "<h2>Usuarios Registrados</h2>";
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>ID</th><th>Usuario</th><th>Nombre</th><th>Email</th><th>Rol</th><th>Activo</th></tr>";
            
            foreach ($usuarios as $usuario) {
                $id = isset($usuario['ID']) ? $usuario['ID'] : $usuario['id'];
                $username = isset($usuario['USERNAME']) ? $usuario['USERNAME'] : $usuario['username'];
                $nombre = isset($usuario['NOMBRE']) ? $usuario['NOMBRE'] : $usuario['nombre'];
                $email = isset($usuario['EMAIL']) ? $usuario['EMAIL'] : $usuario['email'];
                $rol = isset($usuario['ROL']) ? $usuario['ROL'] : $usuario['rol'];
                $activo = isset($usuario['ACTIVO']) ? $usuario['ACTIVO'] : $usuario['activo'];
                
                echo "<tr>";
                echo "<td>$id</td>";
                echo "<td>$username</td>";
                echo "<td>$nombre</td>";
                echo "<td>$email</td>";
                echo "<td>$rol</td>";
                echo "<td>" . ($activo ? 'Sí' : 'No') . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>No hay usuarios registrados.</p>";
        }
        
        // Verificar si existe el usuario dpohidraulica
        $sql = "SELECT * FROM usuarios WHERE username = 'dpohidraulica'";
        $stmt = $pdo->query($sql);
        $dpohidraulica = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($dpohidraulica) {
            $id = isset($dpohidraulica['ID']) ? $dpohidraulica['ID'] : $dpohidraulica['id'];
            echo "<h2>Usuario dpohidraulica</h2>";
            echo "<p style='color: green; font-weight: bold;'>El usuario dpohidraulica existe con ID: $id</p>";
        } else {
            echo "<h2>Usuario dpohidraulica</h2>";
            echo "<p style='color: red; font-weight: bold;'>El usuario dpohidraulica NO existe!</p>";
            
            // Formulario para crear el usuario dpohidraulica
            echo "<h3>Crear Usuario dpohidraulica</h3>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='crear_usuario' value='1'>";
            echo "<button type='submit'>Crear Usuario dpohidraulica</button>";
            echo "</form>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red; font-weight: bold;'>Error al consultar la tabla de usuarios!</p>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red; font-weight: bold;'>Error de conexión a la base de datos!</p>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}

// Procesar formulario para crear el usuario dpohidraulica
if (isset($_POST['crear_usuario'])) {
    try {
        $pdo = getConexion();
        
        // Datos del usuario
        $username = 'dpohidraulica';
        $password = password_hash('dpohidraulica123', PASSWORD_DEFAULT);
        $nombre = 'DEPARTAMENTO HIDRAULICA';
        $email = '<EMAIL>';
        $rol = 'usuario';
        
        // Insertar el usuario
        $sql = "INSERT INTO usuarios (username, password, nombre, email, rol) VALUES (:username, :password, :nombre, :email, :rol)";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':password', $password);
        $stmt->bindParam(':nombre', $nombre);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':rol', $rol);
        
        if ($stmt->execute()) {
            $id = $pdo->lastInsertId();
            echo "<p style='color: green; font-weight: bold;'>Usuario dpohidraulica creado correctamente con ID: $id</p>";
            echo "<p>Recarga la página para verificar.</p>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>Error al crear el usuario dpohidraulica!</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red; font-weight: bold;'>Error al crear el usuario dpohidraulica!</p>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
    }
}
?>
