<?php
// Incluir archivo de permisos
require_once 'permisos.php';

// Verificar que el usuario esté autenticado y tenga permiso
requireLogin();
requirePermiso('admin_usuarios');

$mensaje = '';
$error = '';

// Procesar el formulario
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['guardar_permisos'])) {
        $userId = $_POST['user_id'];
        $permisos = isset($_POST['permisos']) ? $_POST['permisos'] : [];

        error_log("Formulario enviado para usuario $userId con permisos: " . print_r($permisos, true));

        $result = guardarPermisosUsuario($userId, $permisos);
        if ($result === true) {
            $mensaje = 'Los permisos del usuario han sido actualizados correctamente';
            error_log("Permisos guardados correctamente para usuario $userId");
        } else {
            $error = is_string($result) ? $result : 'Error al guardar los permisos del usuario';
            error_log("Error al guardar permisos para usuario $userId: $error");
        }
    } elseif (isset($_POST['limpiar_cache'])) {
        // Limpiar la caché de permisos
        if (isset($_SESSION['permisos_cache'])) {
            unset($_SESSION['permisos_cache']);
            $mensaje = 'La caché de permisos ha sido limpiada correctamente';
            error_log("Caché de permisos limpiada correctamente");
        }
    }
}

// Obtener todos los usuarios
$usuarios = getAllUsers();

// Obtener todos los permisos disponibles
$permisos = getPermisos();

// Obtener el ID del usuario seleccionado
$selectedUserId = isset($_GET['user_id']) ? $_GET['user_id'] : (isset($_POST['user_id']) ? $_POST['user_id'] : null);
$selectedUsername = isset($_GET['username']) ? $_GET['username'] : null;

// Verificar si es el caso especial de dpohidraulica con ID 2
if ($selectedUserId == 2 && $selectedUsername == 'dpohidraulica') {
    // Buscar el ID correcto de dpohidraulica
    try {
        $pdo = getConexion();
        $sql = "SELECT id FROM usuarios WHERE username = 'dpohidraulica'";
        $stmt = $pdo->query($sql);

        if ($stmt->rowCount() > 0) {
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $correctId = isset($result['ID']) ? $result['ID'] : $result['id'];
            error_log("Corrigiendo ID: el usuario dpohidraulica tiene ID: $correctId, no ID: $selectedUserId");
            $selectedUserId = $correctId;
        }
    } catch (PDOException $e) {
        error_log("Error al buscar ID correcto para dpohidraulica: " . $e->getMessage());
    }
}

// Obtener los permisos del usuario seleccionado
$permisosUsuario = $selectedUserId ? getPermisosUsuario($selectedUserId) : [];
error_log("Permisos del usuario seleccionado ($selectedUserId): " . print_r($permisosUsuario, true));

// Obtener el usuario seleccionado
$selectedUser = null;
if ($selectedUserId) {
    foreach ($usuarios as $usuario) {
        if ($usuario['id'] == $selectedUserId) {
            $selectedUser = $usuario;
            break;
        }
    }

    // Si no se encontró el usuario en la lista, buscarlo directamente en la base de datos
    if (!$selectedUser) {
        try {
            $pdo = getConexion();
            $sql = "SELECT id, username, nombre, rol FROM usuarios WHERE id = :id";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':id', $selectedUserId);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $selectedUser = $stmt->fetch(PDO::FETCH_ASSOC);
                // Normalizar nombres de columnas
                $normalizedUser = [];
                foreach ($selectedUser as $key => $value) {
                    $normalizedUser[strtolower($key)] = $value;
                }
                $selectedUser = $normalizedUser;
            }
        } catch (PDOException $e) {
            error_log("Error al buscar usuario por ID: " . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administración de Permisos por Usuario - Sistema de Consultas Oracle</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .permisos-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }

        .usuarios-list {
            flex: 1;
            min-width: 250px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }

        .permisos-form {
            flex: 2;
            min-width: 400px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }

        .usuarios-list h3, .permisos-form h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .usuario-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .usuario-item:hover {
            background-color: #f5f5f5;
        }

        .usuario-item.active {
            background-color: #3498db;
            color: white;
        }

        .permiso-item {
            margin-bottom: 10px;
        }

        .permiso-item label {
            display: flex;
            align-items: center;
        }

        .permiso-item input[type="checkbox"] {
            margin-right: 10px;
        }

        .permiso-descripcion {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-left: 25px;
        }

        .no-user-selected {
            color: #7f8c8d;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
<?php include 'header_with_user.php'; ?>

    <nav>
        <div class="container">
            <ul>
                <li><a href="index.php">Inicio</a></li>
                <?php
                // Verificar si el usuario tiene permiso para ver al menos una consulta
                $mostrarReportes = tienePermiso('ver_reportes');
                if (!$mostrarReportes) {
                    // Verificar permisos específicos para consultas
                    if (tienePermiso('ver_consulta_hidraulica') || tienePermiso('ver_consulta_policia')) {
                        $mostrarReportes = true;
                    }
                }
                if ($mostrarReportes):
                ?>
                    <li><a href="reportes.php">Reportes</a></li>
                <?php endif; ?>
                <?php if (tienePermiso('admin_usuarios')): ?>
                    <li><a href="admin_usuarios.php">Administrar Usuarios</a></li>
                    <li><a href="admin_permisos_usuario.php" class="active">Administrar Permisos</a></li>
                    <li><a href="corregir_permisos.php">Corregir Permisos</a></li>
                <?php endif; ?>
                <li><a href="logout.php">Cerrar Sesión (<?php echo htmlspecialchars($_SESSION['user_username']); ?>)</a></li>
            </ul>
        </div>
    </nav>

    <main class="container">
        <h2>Administración de Permisos por Usuario</h2>

        <?php if (!empty($mensaje)): ?>
            <div class="success"><?php echo $mensaje; ?></div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>

        <p>En esta sección puedes configurar los permisos específicos para cada usuario del sistema.</p>

        <div style="display: flex; gap: 10px; margin-bottom: 20px;">
            <form method="post">
                <button type="submit" name="limpiar_cache" class="btn btn-warning">Limpiar caché de permisos</button>
                <small style="margin-left: 10px;">Usa esta opción si los cambios en permisos no se reflejan inmediatamente</small>
            </form>

            <a href="limpiar_sesion.php" class="btn btn-danger" style="margin-left: 20px;">Cerrar sesión y limpiar caché</a>
            <small style="margin-left: 10px;">Usa esta opción si los problemas persisten</small>
        </div>

        <div class="permisos-container">
            <div class="usuarios-list">
                <h3>Usuarios</h3>
                <?php if ($usuarios): ?>
                    <?php foreach ($usuarios as $usuario): ?>
                        <div class="usuario-item <?php echo $selectedUserId == $usuario['id'] ? 'active' : ''; ?>">
                            <a href="admin_permisos_usuario.php?user_id=<?php echo $usuario['id']; ?>" style="display: block; text-decoration: none; color: inherit;">
                                <strong><?php echo htmlspecialchars($usuario['username']); ?></strong>
                                <div><?php echo htmlspecialchars($usuario['nombre']); ?></div>
                                <div><small><?php echo htmlspecialchars($usuario['rol']); ?></small></div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p>No hay usuarios registrados</p>
                <?php endif; ?>
            </div>

            <div class="permisos-form">
                <?php if ($selectedUser): ?>
                    <h3>Permisos para <?php echo htmlspecialchars($selectedUser['nombre']); ?> (<?php echo htmlspecialchars($selectedUser['username']); ?>)</h3>

                    <div style="margin-bottom: 20px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                        <p><strong>ID de usuario:</strong> <?php echo $selectedUser['id']; ?></p>
                        <p><strong>Rol:</strong> <?php echo htmlspecialchars($selectedUser['rol']); ?></p>
                        <p><strong>Permisos actuales:</strong> <?php echo implode(', ', $permisosUsuario); ?></p>

                        <?php if ($selectedUser['id'] == 2): ?>
                        <div class="alert alert-info" style="margin-top: 10px; padding: 10px; background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; color: #0c5460;">
                            <p><strong>Nota:</strong> Para este usuario, puedes usar la herramienta especial para eliminar permisos innecesarios.</p>
                            <a href="modificar_permisos_usuario2.php" class="btn btn-info" style="margin-top: 5px;">Modificar Permisos Usuario ID 2</a>
                        </div>
                        <?php endif; ?>
                    </div>

                    <form method="post">
                        <input type="hidden" name="user_id" value="<?php echo $selectedUser['id']; ?>">

                        <?php foreach ($permisos as $codigo => $descripcion): ?>
                            <div class="permiso-item">
                                <label>
                                    <input type="checkbox" name="permisos[]" value="<?php echo $codigo; ?>"
                                        <?php echo in_array($codigo, $permisosUsuario) ? 'checked' : ''; ?>>
                                    <?php echo $codigo; ?>
                                </label>
                                <div class="permiso-descripcion"><?php echo $descripcion; ?></div>
                            </div>
                        <?php endforeach; ?>

                        <button type="submit" name="guardar_permisos" class="btn btn-success" style="margin-top: 15px;">Guardar Permisos</button>
                    </form>
                <?php else: ?>
                    <div class="no-user-selected">
                        <p>Selecciona un usuario de la lista para configurar sus permisos</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Sistema de Consultas Oracle. Todos los derechos reservados.</p>
        </div>
    </footer>
</body>
</html>
