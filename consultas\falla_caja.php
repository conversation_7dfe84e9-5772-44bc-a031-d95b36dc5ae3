<?php
/**
 * Consulta de Falla de Caja
 *
 * Este archivo contiene la consulta para el reporte de Falla de Caja.
 */

// Verificar si el usuario está autenticado y tiene permiso
if (!isLoggedIn() || (!isAdmin() && !tienePermiso('ver_reporte_falla_caja'))) {
    echo "<p class='error'>No tienes permiso para acceder a este reporte.</p>";
    return;
}

try {
    $pdo = getConexion();

    // Crear tabla temporal para almacenar los resultados
    $pdo->exec("
        BEGIN
            EXECUTE IMMEDIATE 'DROP TABLE TEMP_FALLA_CAJA';
        EXCEPTION
            WHEN OTHERS THEN
                IF SQLCODE != -942 THEN
                    RAISE;
                END IF;
        END;
    ");

    $pdo->exec("
        CREATE GLOBAL TEMPORARY TABLE TEMP_FALLA_CAJA (
            LEGAJO VARCHAR2(10),
            NOMBRE VARCHAR2(100),
            IMPOR<PERSON> NUMBER(10,2),
            FECHA DATE
        ) ON COMMIT PRESERVE ROWS
    ");

    // Insertar datos de ejemplo en la tabla temporal
    $pdo->exec("
        INSERT INTO TEMP_FALLA_CAJA (LEGAJO, NOMBRE, IMPORTE, FECHA)
        VALUES ('1001', 'JUAN PEREZ', 5000.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    $pdo->exec("
        INSERT INTO TEMP_FALLA_CAJA (LEGAJO, NOMBRE, IMPORTE, FECHA)
        VALUES ('1002', 'MARIA GOMEZ', 4500.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    $pdo->exec("
        INSERT INTO TEMP_FALLA_CAJA (LEGAJO, NOMBRE, IMPORTE, FECHA)
        VALUES ('1003', 'CARLOS RODRIGUEZ', 4800.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    $pdo->exec("
        INSERT INTO TEMP_FALLA_CAJA (LEGAJO, NOMBRE, IMPORTE, FECHA)
        VALUES ('1004', 'ANA MARTINEZ', 5200.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    $pdo->exec("
        INSERT INTO TEMP_FALLA_CAJA (LEGAJO, NOMBRE, IMPORTE, FECHA)
        VALUES ('1005', 'PEDRO SANCHEZ', 4900.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    // Ejecutar la consulta final
    $sql = "SELECT LEGAJO, NOMBRE, IMPORTE, TO_CHAR(FECHA, 'DD/MM/YYYY') AS FECHA FROM TEMP_FALLA_CAJA ORDER BY LEGAJO";
    $stmt = $pdo->query($sql);

    // Si estamos en modo de exportación a Excel, no mostrar nada en pantalla
    if (basename($_SERVER['PHP_SELF']) === 'exportar_excel.php') {
        // No hacer nada, solo devolver $stmt para que exportar_excel.php lo use
        return;
    }

    // Si estamos en modo de visualización normal, mostrar los resultados en pantalla
    echo "<h3>Resultados de Falla de Caja</h3>";
    echo "<p>Este reporte muestra la falla de caja para los empleados.</p>";

} catch (PDOException $e) {
    echo "<p class='error'>Error en la consulta: " . $e->getMessage() . "</p>";
}
?>
