<?php
/**
 * Consulta de San Nicolás Mensual
 *
 * Este archivo contiene la consulta para el reporte de San Nicolás Mensual.
 */

// Verificar si el usuario está autenticado y tiene permiso
if (!isLoggedIn() || (!isAdmin() && !tienePermiso('ver_reporte_san_nicolas'))) {
    echo "<p class='error'>No tienes permiso para acceder a este reporte.</p>";
    return;
}

try {
    $pdo = getConexion();

    // Crear tabla temporal para almacenar los resultados
    $pdo->exec("
        BEGIN
            EXECUTE IMMEDIATE 'DROP TABLE TEMP_SAN_NICOLAS';
        EXCEPTION
            WHEN OTHERS THEN
                IF SQLCODE != -942 THEN
                    RAISE;
                END IF;
        END;
    ");

    $pdo->exec("
        CREATE GLOBAL TEMPORARY TABLE TEMP_SAN_NICOLAS (
            LEGAJO VARCHAR2(10),
            NOMBRE VARCHAR2(100),
            SECTOR VARCHAR2(50),
            IMPORTE NUMBER(10,2),
            FECHA DATE
        ) ON COMMIT PRESERVE ROWS
    ");

    // Insertar datos de ejemplo en la tabla temporal
    $pdo->exec("
        INSERT INTO TEMP_SAN_NICOLAS (LEGAJO, NOMBRE, SECTOR, IMPORTE, FECHA)
        VALUES ('2001', 'ROBERTO FERNANDEZ', 'ADMINISTRACION', 6500.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    $pdo->exec("
        INSERT INTO TEMP_SAN_NICOLAS (LEGAJO, NOMBRE, SECTOR, IMPORTE, FECHA)
        VALUES ('2002', 'LAURA GONZALEZ', 'VENTAS', 7200.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    $pdo->exec("
        INSERT INTO TEMP_SAN_NICOLAS (LEGAJO, NOMBRE, SECTOR, IMPORTE, FECHA)
        VALUES ('2003', 'MARTIN LOPEZ', 'PRODUCCION', 6800.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    $pdo->exec("
        INSERT INTO TEMP_SAN_NICOLAS (LEGAJO, NOMBRE, SECTOR, IMPORTE, FECHA)
        VALUES ('2004', 'CAROLINA DIAZ', 'RECURSOS HUMANOS', 7000.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    $pdo->exec("
        INSERT INTO TEMP_SAN_NICOLAS (LEGAJO, NOMBRE, SECTOR, IMPORTE, FECHA)
        VALUES ('2005', 'JAVIER TORRES', 'FINANZAS', 7500.00, TO_DATE('2023-05-01', 'YYYY-MM-DD'))
    ");

    // Ejecutar la consulta final
    $sql = "SELECT LEGAJO, NOMBRE, SECTOR, IMPORTE, TO_CHAR(FECHA, 'DD/MM/YYYY') AS FECHA FROM TEMP_SAN_NICOLAS ORDER BY SECTOR, LEGAJO";
    $stmt = $pdo->query($sql);

    // Si estamos en modo de exportación a Excel, no mostrar nada en pantalla
    if (basename($_SERVER['PHP_SELF']) === 'exportar_excel.php') {
        // No hacer nada, solo devolver $stmt para que exportar_excel.php lo use
        return;
    }

    // Si estamos en modo de visualización normal, mostrar los resultados en pantalla
    echo "<h3>Resultados de San Nicolás Mensual</h3>";
    echo "<p>Este reporte muestra los datos mensuales de San Nicolás.</p>";

} catch (PDOException $e) {
    echo "<p class='error'>Error en la consulta: " . $e->getMessage() . "</p>";
}
?>
