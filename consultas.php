<?php
// Definir las categorías de consultas
$categorias = [
    'sin_ajuste' => [
        'titulo' => 'Sin Ajuste',
        'descripcion' => 'Consultas sin aplicar ajustes'
    ],
    'con_ajuste' => [
        'titulo' => 'Con Ajuste',
        'descripcion' => 'Consultas con ajustes aplicados'
    ]
];

// Definir las consultas disponibles
$consultas = [
    // Categoría: Sin Ajuste
    'falla_caja' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'Falla de Caja',
        'descripcion' => 'Consulta de datos de falla de caja.',
        'sql' => "
                 select a.centro,C.NOMCENTRO,a.sector,C.NOMSECTOR,a.padrond,a.cuil,a.apynom,a.categ,
A.ESCALAFON,A.TACAREDU,
NVL(TO_CHAR(b.A05),'0,00') AS A05,
NVL(TO_CHAR(b.A79),'0,00') AS A79
--Nvl(A05, 0) + Nvl(A79, 0) Suma   
from sueldo0425 a
join
(

SELECT TO_CHAR (a.CENTRO, '00') CENTRO,TO_CHAR (A.sector, '000') SECTOR,TO_CHAR(A.padrond,'0000000') padron,B.* 
FROM exp5.sueldo0425 A
join 
(
select * from ( 
select padronD,codigo,importe from EXP5.CIESCU0425
)
PIVOT
(
    sum(importe) 
    FOR codigo IN (
'A05' A05,
'A79' A79
)) 
)
B ON (b.padrond=a.padrond) --(B.Q38>0 OR B.Q39>0 OR B.Q40>0 OR B.Q62>0 OR B.Q63>0);--; ---and a01>0 and a27>0;
UNION
SELECT TO_CHAR (a.CENTRO, '00') CENTRO,TO_CHAR (A.sector, '000') SECTOR,TO_CHAR(A.padrond,'0000000') padron,B.* 
FROM exp5.sueldo0425 A
join 
(
select * from ( 
select padronD,codigo,importe from EXP5.CIMENS0425
)
PIVOT
(
    sum(importe) 
    FOR codigo IN (
'A05' A05,
'A79' A79
)) 
)
B ON (b.padrond=a.padrond) --(B.Q38>0 OR B.Q39>0 OR B.Q40>0 OR B.Q62>0 OR B.Q63>0);--; ---and a01>0 and a27>0;
)


B ON (b.padrond=a.padrond)  AND (B.A05>0 OR B.A79>0 OR B.A05<0 OR B.A79<0 ) 

JOIN
REPARTICIONES C
ON a.padrond=b.padrond AND A.CENTRO=C.CENTRO AND A.SECTOR=C.SECTOR",
        'permiso' => 'ver_reporte_falla_caja'
    ],
    'san_nicolas_mensual' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'San Nicolás Mensual',
        'descripcion' => 'Consulta mensual de San Nicolás.',
        'sql' => "select a.centro,a.sector,a.padrond,a.apynom,a.cuil,a.planta,a.escalafon,a.categ,a.antig,a.tacaredu,
a.tasaredu,a.totdesc,a.neto,b.codigo,b.importe
from sueldo0425 a,ciescu0425 b
where a.padrond=b.padrond and a.centro=63 and (a.sector=419 OR a.sector=377)
order by a.padrond,codigo
",
        'permiso' => 'ver_reporte_san_nicolas'
    ],
    'santisimo_sacramento_mensual' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'Santísimo Sacramento Mensual',
        'descripcion' => 'Consulta mensual de Santísimo Sacramento.',
        'sql' => "select a.centro,a.sector,a.padrond,a.apynom,a.cuil,a.planta,a.escalafon,a.categ,a.antig,a.tacaredu,
a.tasaredu,a.totdesc,a.neto,b.codigo,b.importe
from sueldo0425 a,ciescu0425 b
where a.padrond=b.padrond and a.centro=64 and a.sector=443
UNION
select a.centro,a.sector,a.padrond,a.apynom,a.cuil,a.planta,a.escalafon,a.categ,a.antig,a.tacaredu,
a.tasaredu,a.totdesc,a.neto,b.codigo,b.importe
from sueldo0425 a,ciescu0425 b
where a.padrond=b.padrond and a.centro=63 and a.sector=443
UNION
select a.centro,a.sector,a.padrond,a.apynom,a.cuil,a.planta,a.escalafon,a.categ,a.antig,a.tacaredu,
a.tasaredu,a.totdesc,a.neto,b.codigo,b.importe
from sueldo0425 a,ciescu0425 b
where a.padrond=b.padrond and a.centro=29 and a.sector=916
UNION
select a.centro,a.sector,a.padrond,a.apynom,a.cuil,a.planta,a.escalafon,a.categ,a.antig,a.tacaredu,
a.tasaredu,a.totdesc,a.neto,b.codigo,b.importe
from sueldo0425 a,ciescu0425 b
where a.padrond=b.padrond and a.centro=67 and a.sector=916
order by padrond,codigo",
        'permiso' => 'ver_reporte_santisimo_sacramento'
    ],
    'universidad_catolica_mensual' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'Universidad Católica Mensual',
        'descripcion' => 'Consulta mensual de Universidad Católica.',
        'sql' => "SELECT
                    a.CENTRO,
                    a.SECTOR,
                    a.PADROND,
                    a.APYNOM,
                    a.CUIL,
                    a.CATEG,
                    a.ESCALAFON,
                    ASIGAPOR,
                    RETROMESANT,
                    OTRORETRO,
                    SALARIOSINAPO,
                    APORJUB
                FROM sueldo0425 a
                JOIN imponi0425B b ON a.PADROND = b.PADROND
                WHERE a.CENTRO = 25
                ORDER BY a.SECTOR, a.PADROND",
        'permiso' => 'ver_reporte_universidad_catolica'
    ],
    'imponible_poder_judicial' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'Imponible Poder Judicial',
        'descripcion' => 'Consulta de imponible para el Poder Judicial.',
        'sql' => "SELECT
                    a.CENTRO,
                    a.SECTOR,
                    a.PADROND,
                    a.APYNOM,
                    a.CUIL,
                    a.CATEG,
                    a.ESCALAFON,
                    ASIGAPOR,
                    RETROMESANT,
                    OTRORETRO,
                    SALARIOSINAPO,
                    APORJUB
                FROM sueldo0425 a
                JOIN imponi0425B b ON a.PADROND = b.PADROND
                WHERE a.CENTRO = 26
                ORDER BY a.SECTOR, a.PADROND",
        'permiso' => 'ver_reporte_poder_judicial'
    ],
    'reparto_poder_judicial' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'Reparto Poder Judicial',
        'descripcion' => 'Consulta de reparto para el Poder Judicial.',
        'sql' => "SELECT
                    a.CENTRO,
                    a.SECTOR,
                    a.PADROND,
                    a.APYNOM,
                    a.CUIL,
                    a.CATEG,
                    a.ESCALAFON,
                    ASIGAPOR,
                    RETROMESANT,
                    OTRORETRO,
                    SALARIOSINAPO,
                    APORJUB
                FROM sueldo0425 a
                JOIN imponi0425B b ON a.PADROND = b.PADROND
                WHERE a.CENTRO = 27
                ORDER BY a.SECTOR, a.PADROND",
        'permiso' => 'ver_reporte_reparto_poder_judicial'
    ],

    // Consultas originales (ahora en categoría "Con Ajuste")
    'imponible_hidraulica' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Imponible Hidráulica - Centro 22',
        'descripcion' => 'Consulta de datos de imponible hidráulica para el centro 22. Incluye información sobre empleados, salarios y aportes.',
        'sql' => "SELECT
                    a.CENTRO,
                    a.SECTOR,
                    a.PADROND,
                    a.APYNOM,
                    a.CUIL,
                    a.ANTIG,
                    a.PERM_CAT,
                    a.CATEG,
                    a.ESCALAFON,
                    a.FNACIM,
                    ASIGAPOR,
                    RETROMESANT,
                    OTRORETRO,
                    OTROCONCEPSINAPO,
                    SALARIOSINAPO,
                    APORJUB,
                    H01,
                    Z01
                FROM sueldo0425 a
                JOIN imponi0425B b ON a.PADROND = b.PADROND
                WHERE a.CENTRO = 22
                ORDER BY a.CENTRO, a.SECTOR, a.PADROND",
        'permiso' => 'ver_reporte_hidraulica'
    ],
    'imponible_policia' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Imponible Policía',
        'descripcion' => 'Consulta de datos de imponible para el personal de policía. Incluye información sobre empleados, salarios y aportes.',
        'sql' => "SELECT
                    a.CENTRO,
                    a.SECTOR,
                    a.PADROND,
                    a.APYNOM,
                    a.CUIL,
                    a.ANTIG,
                    a.PERM_CAT,
                    a.CATEG,
                    a.ESCALAFON,
                    a.FNACIM,
                    ASIGAPOR,
                    RETROMESANT,
                    OTRORETRO,
                    OTROCONCEPSINAPO,
                    SALARIOSINAPO,
                    APORJUB
                FROM sueldo0425 a
                JOIN imponi0425B b ON a.PADROND = b.PADROND
                WHERE a.CENTRO = 11
                ORDER BY a.CENTRO, a.SECTOR, a.PADROND",
        'permiso' => 'ver_consulta_policia'
    ],
    'resumen_centros' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Resumen por Centros',
        'descripcion' => 'Resumen de imponibles agrupados por centro. Muestra totales y promedios.',
        'sql' => "SELECT
                    a.CENTRO,
                    COUNT(a.PADROND) as CANTIDAD,
                    SUM(ASIGAPOR) as TOTAL_ASIGAPOR,
                    AVG(ASIGAPOR) as PROMEDIO_ASIGAPOR,
                    SUM(APORJUB) as TOTAL_APORJUB,
                    AVG(APORJUB) as PROMEDIO_APORJUB
                FROM sueldo0425 a
                JOIN imponi0425B b ON a.PADROND = b.PADROND
                GROUP BY a.CENTRO
                ORDER BY a.CENTRO",
        'permiso' => 'ver_reporte_resumen_centros'
    ],
    'resumen_sectores' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Resumen por Sectores',
        'descripcion' => 'Resumen de imponibles agrupados por sector. Muestra totales y promedios.',
        'sql' => "SELECT
                    a.CENTRO,
                    a.SECTOR,
                    COUNT(a.PADROND) as CANTIDAD,
                    SUM(ASIGAPOR) as TOTAL_ASIGAPOR,
                    AVG(ASIGAPOR) as PROMEDIO_ASIGAPOR,
                    SUM(APORJUB) as TOTAL_APORJUB,
                    AVG(APORJUB) as PROMEDIO_APORJUB
                FROM sueldo0425 a
                JOIN imponi0425B b ON a.PADROND = b.PADROND
                GROUP BY a.CENTRO, a.SECTOR
                ORDER BY a.CENTRO, a.SECTOR",
        'permiso' => 'ver_reporte_resumen_sectores'
    ]
];
?>
