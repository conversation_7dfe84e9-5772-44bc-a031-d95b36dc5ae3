<?php
// Incluir archivos necesarios
require_once 'conexion.php';
require_once 'permisos.php';
require_once 'auth.php';

// Verificar si el usuario está autenticado
requireLogin();

// Verificar si se ha especificado una consulta
if (!isset($_GET['consulta'])) {
    $_SESSION['error_exportar'] = 'No se ha especificado una consulta para exportar';
    header('Location: reportes.php');
    exit;
}

$consulta = $_GET['consulta'];

// Cargar las definiciones de consultas
require_once 'consultas_sql.php';

// Verificar que la consulta exista
if (!isset($consultas[$consulta])) {
    $_SESSION['error_exportar'] = 'La consulta especificada no existe';
    header('Location: reportes.php');
    exit;
}

// Verificar permiso para la consulta
if (!tienePermiso($consultas[$consulta]['permiso'])) {
    $_SESSION['error_permiso'] = 'No tienes permiso para exportar esta consulta';
    header('Location: reportes.php');
    exit;
}

try {
    error_log("Iniciando exportación a Excel para consulta: $consulta");
    $pdo = getConexion();

    // Obtener información de la consulta
    $archivoConsulta = $consultas[$consulta]['archivo'];
    $tipoConsulta = isset($consultas[$consulta]['tipo']) ? $consultas[$consulta]['tipo'] : 'php';
    error_log("Archivo de consulta a ejecutar: $archivoConsulta (tipo: $tipoConsulta)");

    // Verificar que el archivo exista
    if (!file_exists($archivoConsulta)) {
        throw new Exception("El archivo de consulta no existe: $archivoConsulta");
    }

    // Ejecutar la consulta según su tipo
    if ($tipoConsulta === 'sql') {
        // Incluir el archivo para ejecutar scripts SQL
        require_once 'ejecutar_script_sql.php';

        // Ejecutar el script SQL
        $stmt = ejecutarScriptSQL($archivoConsulta);
    } else if ($tipoConsulta === 'sql_directo') {
        // Ejecutar la consulta SQL directamente
        $sql = $consultas[$consulta]['sql'];

        // Ejecutar la consulta
        $stmt = $pdo->query($sql);
    } else {
        // Crear un buffer para capturar la salida del archivo de consulta
        ob_start();

        // Incluir el archivo de consulta
        include $archivoConsulta;

        // Limpiar el buffer (para evitar que se muestre cualquier salida HTML)
        ob_end_clean();
    }

    // Verificar que la variable $stmt esté definida
    if (!isset($stmt) || $stmt === null) {
        throw new Exception("La consulta no generó resultados. Verifica el archivo de consulta: $archivoConsulta");
    }

    error_log("Consulta ejecutada correctamente");

    // Incluir directamente los archivos de PHPExcel
    error_log("Cargando librerías PHPExcel");
    require_once 'PHPExcel.php';
    require_once 'PHPExcel/IOFactory.php';
    error_log("Librerías PHPExcel cargadas correctamente");

    $objPHPExcel = new PHPExcel();
    $objPHPExcel->setActiveSheetIndex(0);
    $sheet = $objPHPExcel->getActiveSheet();
    error_log("Objeto PHPExcel creado correctamente");

    // Establecer propiedades del documento
    $objPHPExcel->getProperties()
        ->setCreator("Sistema de Reportes de Sueldos")
        ->setLastModifiedBy("Sistema de Reportes de Sueldos")
        ->setTitle($consultas[$consulta]['titulo'])
        ->setSubject($consultas[$consulta]['titulo'])
        ->setDescription($consultas[$consulta]['descripcion'])
        ->setKeywords("excel, reporte, " . $consulta)
        ->setCategory("Reportes");

    // Configurar márgenes y orientación de página
    $sheet->getPageSetup()->setOrientation(PHPExcel_Worksheet_PageSetup::ORIENTATION_LANDSCAPE);
    $sheet->getPageSetup()->setPaperSize(PHPExcel_Worksheet_PageSetup::PAPERSIZE_A4);
    $sheet->getPageSetup()->setFitToPage(true);
    $sheet->getPageSetup()->setFitToWidth(1);
    $sheet->getPageSetup()->setFitToHeight(0);

    // Establecer márgenes (en pulgadas)
    $sheet->getPageMargins()->setTop(0.5);
    $sheet->getPageMargins()->setRight(0.5);
    $sheet->getPageMargins()->setLeft(0.5);
    $sheet->getPageMargins()->setBottom(0.5);

    // Establecer título de la hoja
    $sheet->setTitle($consultas[$consulta]['titulo']);

    // Obtener los nombres de las columnas
    $columnNames = [];
    $columnCount = $stmt->columnCount();
    error_log("Número de columnas en el resultado: $columnCount");

    for ($i = 0; $i < $columnCount; $i++) {
        $meta = $stmt->getColumnMeta($i);
        $columnNames[] = $meta['name'];
    }
    error_log("Nombres de columnas obtenidos: " . implode(', ', $columnNames));

    // Encabezados
    $col = 0;
    foreach ($columnNames as $header) {
        $sheet->setCellValueByColumnAndRow($col, 1, $header);

        // Aplicar estilo a los encabezados
        $sheet->getStyleByColumnAndRow($col, 1)
            ->getFont()
            ->setBold(true);

        // Ajustar ancho de columna según el tipo de dato
        // En lugar de autosize, establecemos anchos específicos para mejorar el ajuste
        $columnWidth = 12; // Ancho predeterminado

        // Ajustar ancho según el nombre de la columna
        if (in_array($header, ['APYNOM', 'NOMBRE', 'DESCRIPCION'])) {
            $columnWidth = 30; // Columnas de nombres más anchas
        } elseif (in_array($header, ['CUIL', 'CUIT'])) {
            $columnWidth = 15; // Columnas de CUIL/CUIT
        } elseif (strpos($header, 'FECHA') !== false || $header == 'FNACIM') {
            $columnWidth = 12; // Columnas de fechas
        } elseif (in_array($header, ['CENTRO', 'SECTOR', 'PADROND', 'ID'])) {
            $columnWidth = 8; // Columnas de IDs más estrechas
        } elseif (strpos($header, 'TOTAL') !== false ||
                 strpos($header, 'ASIGAPOR') !== false ||
                 strpos($header, 'SALARIO') !== false ||
                 strpos($header, 'APORJUB') !== false) {
            $columnWidth = 14; // Columnas de importes monetarios
        }

        $sheet->getColumnDimensionByColumn($col)->setWidth($columnWidth);

        error_log("Escribiendo encabezado en columna $col: $header (ancho: $columnWidth)");
        $col++;
    }

    // Aplicar formato a la fila de encabezados
    $sheet->getStyle('A1:' . PHPExcel_Cell::stringFromColumnIndex($col-1) . '1')
        ->getFill()
        ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
        ->getStartColor()
        ->setRGB('DDDDDD');

    // Cuerpo
    $row = 2;
    $rowCount = 0;

    // Definir formatos para diferentes tipos de datos
    $formatoMoneda = '#,##0.00';
    $formatoFecha = 'DD/MM/YYYY';

    while ($fila = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $col = 0;
        foreach ($columnNames as $key) {
            $valor = isset($fila[$key]) ? $fila[$key] : '';

            // Determinar el tipo de dato y aplicar formato adecuado
            if (is_numeric($valor) &&
                (strpos($key, 'TOTAL') !== false ||
                 strpos($key, 'ASIGAPOR') !== false ||
                 strpos($key, 'SALARIO') !== false ||
                 strpos($key, 'APORJUB') !== false ||
                 strpos($key, 'RETRO') !== false ||
                 strpos($key, 'IMPORTE') !== false)) {

                // Formatear como moneda
                $sheet->setCellValueByColumnAndRow($col, $row, (float)$valor);
                $sheet->getStyleByColumnAndRow($col, $row)
                      ->getNumberFormat()
                      ->setFormatCode($formatoMoneda);

            } elseif (strpos($key, 'FECHA') !== false || $key == 'FNACIM') {
                // Intentar formatear como fecha si es una fecha válida
                if (!empty($valor)) {
                    try {
                        // Intentar convertir a fecha de Excel
                        if (strtotime($valor) !== false) {
                            $dateValue = PHPExcel_Shared_Date::PHPToExcel(new DateTime($valor));
                            $sheet->setCellValueByColumnAndRow($col, $row, $dateValue);
                            $sheet->getStyleByColumnAndRow($col, $row)
                                  ->getNumberFormat()
                                  ->setFormatCode($formatoFecha);
                        } else {
                            // Si no se puede convertir, mostrar como texto
                            $sheet->setCellValueByColumnAndRow($col, $row, $valor);
                        }
                    } catch (Exception $e) {
                        // Si hay error, mostrar como texto
                        $sheet->setCellValueByColumnAndRow($col, $row, $valor);
                    }
                } else {
                    $sheet->setCellValueByColumnAndRow($col, $row, '');
                }
            } else {
                // Otros valores se muestran como texto
                $sheet->setCellValueByColumnAndRow($col, $row, $valor);
            }

            $col++;
        }

        // Alternar colores de fila para mejor legibilidad
        if ($row % 2 == 0) {
            $sheet->getStyle('A' . $row . ':' . PHPExcel_Cell::stringFromColumnIndex($col-1) . $row)
                  ->getFill()
                  ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
                  ->getStartColor()
                  ->setRGB('F9F9F9');
        }

        $row++;
        $rowCount++;
    }

    // Agregar bordes a la tabla
    $sheet->getStyle('A1:' . PHPExcel_Cell::stringFromColumnIndex($col-1) . ($row-1))
          ->getBorders()
          ->getAllBorders()
          ->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);

    error_log("Número de filas escritas en el Excel: $rowCount");

    // Asegurarse de que no haya salida previa
    if (ob_get_length()) {
        ob_end_clean();
    }

    // Desactivar el búfer de salida
    ob_start();

    // Crear el escritor de Excel
    error_log("Creando escritor Excel5 (formato .xls)");
    $writer = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');

    // Guardar el archivo en el búfer
    error_log("Guardando archivo Excel en el búfer");
    $writer->save('php://output');

    // Obtener el contenido del búfer
    $excelOutput = ob_get_contents();

    // Limpiar el búfer
    ob_end_clean();

    // Calcular la longitud del contenido
    $fileSize = strlen($excelOutput);
    error_log("Tamaño del archivo Excel: $fileSize bytes");

    // Generar nombre de archivo
    $filename = 'reporte_' . $consulta . '_' . date('Ymd_His') . '.xls';

    // Encabezados para descarga
    error_log("Preparando encabezados para descarga");
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . $fileSize);
    header('Cache-Control: max-age=0');
    header('Pragma: public');
    header('Expires: 0');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Cache-Control: private', false);
    error_log("Encabezados configurados correctamente");

    // Enviar el contenido
    echo $excelOutput;
    error_log("Archivo Excel enviado correctamente");
    exit;
} catch (PDOException $e) {
    $error = 'Error en la conexión o la consulta: ' . $e->getMessage();
    error_log("Error PDO al exportar: " . $e->getMessage());

    // Guardar el error en la sesión para mostrarlo después de la redirección
    $_SESSION['error_exportar'] = $error;

    // Mostrar información detallada para depuración
    if (isAdmin()) {
        echo "<h1>Error al exportar a Excel</h1>";
        echo "<p>Se ha producido un error al intentar exportar a Excel:</p>";
        echo "<pre>" . htmlspecialchars($error) . "</pre>";
        echo "<p>Rastreo de pila:</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        exit;
    }

    header('Location: reportes.php?consulta=' . urlencode($consulta));
    exit;
} catch (Exception $e) {
    $error = 'Error al generar el archivo Excel: ' . $e->getMessage();
    error_log("Error general al exportar: " . $e->getMessage());

    // Guardar el error en la sesión para mostrarlo después de la redirección
    $_SESSION['error_exportar'] = $error;

    // Mostrar información detallada para depuración
    if (isAdmin()) {
        echo "<h1>Error al exportar a Excel</h1>";
        echo "<p>Se ha producido un error al intentar exportar a Excel:</p>";
        echo "<pre>" . htmlspecialchars($error) . "</pre>";
        echo "<p>Rastreo de pila:</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        exit;
    }

    header('Location: reportes.php?consulta=' . urlencode($consulta));
    exit;
}
?>
