<?php
/**
 * Archivo con configuraciones alternativas para la conexión a Oracle
 * 
 * Este archivo contiene diferentes opciones de configuración para conectarse a Oracle.
 * Puedes probar estas configuraciones si la conexión principal no funciona.
 */

// Configuración de la conexión a Oracle
$host = '**********';
$port = '1521';
$service = 'DESA920';
$sid = 'DESA920';
$user = 'EXP5';
$pass = 'E*x20_22.';

// Opción 1: Usando SID (formato tradicional)
function getConexionSID() {
    global $host, $port, $sid, $user, $pass;
    
    $dsn = "oci:dbname=$host:$port:$sid";
    
    try {
        $pdo = new PDO($dsn, $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_CASE, PDO::CASE_UPPER);
        
        return $pdo;
    } catch (PDOException $e) {
        throw $e;
    }
}

// Opción 2: Usando Service Name
function getConexionService() {
    global $host, $port, $service, $user, $pass;
    
    $dsn = "oci:dbname=//$host:$port/$service";
    
    try {
        $pdo = new PDO($dsn, $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_CASE, PDO::CASE_UPPER);
        
        return $pdo;
    } catch (PDOException $e) {
        throw $e;
    }
}

// Opción 3: Usando TNS (requiere archivo tnsnames.ora)
function getConexionTNS() {
    global $user, $pass;
    
    $dsn = "oci:dbname=DESA920";
    
    try {
        $pdo = new PDO($dsn, $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_CASE, PDO::CASE_UPPER);
        
        return $pdo;
    } catch (PDOException $e) {
        throw $e;
    }
}

// Opción 4: Usando formato completo
function getConexionCompleta() {
    global $host, $port, $service, $user, $pass;
    
    $dsn = "oci:dbname=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=$host)(PORT=$port))(CONNECT_DATA=(SERVICE_NAME=$service)))";
    
    try {
        $pdo = new PDO($dsn, $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_CASE, PDO::CASE_UPPER);
        
        return $pdo;
    } catch (PDOException $e) {
        throw $e;
    }
}

// Opción 5: Usando SID con formato alternativo
function getConexionSIDAlternativo() {
    global $host, $port, $sid, $user, $pass;
    
    $dsn = "oci:dbname={$host}:{$port}/{$sid}";
    
    try {
        $pdo = new PDO($dsn, $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_CASE, PDO::CASE_UPPER);
        
        return $pdo;
    } catch (PDOException $e) {
        throw $e;
    }
}

// Función para probar todas las conexiones
function probarTodasLasConexiones() {
    $resultados = [];
    
    // Probar Opción 1: SID
    try {
        $pdo = getConexionSID();
        $resultados['sid'] = "Conexión exitosa usando SID";
    } catch (PDOException $e) {
        $resultados['sid'] = "Error: " . $e->getMessage();
    }
    
    // Probar Opción 2: Service Name
    try {
        $pdo = getConexionService();
        $resultados['service'] = "Conexión exitosa usando Service Name";
    } catch (PDOException $e) {
        $resultados['service'] = "Error: " . $e->getMessage();
    }
    
    // Probar Opción 3: TNS
    try {
        $pdo = getConexionTNS();
        $resultados['tns'] = "Conexión exitosa usando TNS";
    } catch (PDOException $e) {
        $resultados['tns'] = "Error: " . $e->getMessage();
    }
    
    // Probar Opción 4: Formato completo
    try {
        $pdo = getConexionCompleta();
        $resultados['completa'] = "Conexión exitosa usando formato completo";
    } catch (PDOException $e) {
        $resultados['completa'] = "Error: " . $e->getMessage();
    }
    
    // Probar Opción 5: SID Alternativo
    try {
        $pdo = getConexionSIDAlternativo();
        $resultados['sid_alternativo'] = "Conexión exitosa usando SID alternativo";
    } catch (PDOException $e) {
        $resultados['sid_alternativo'] = "Error: " . $e->getMessage();
    }
    
    return $resultados;
}
?>
