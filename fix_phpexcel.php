<?php
/**
 * Script para corregir la sintaxis de acceso a caracteres en strings en PHPExcel
 * 
 * Este script reemplaza la sintaxis de llaves {} por la sintaxis de corchetes []
 * para acceder a caracteres en strings, que fue deprecada en PHP 7.4 y eliminada en PHP 8.0.
 */

// Directorio donde se encuentra PHPExcel
$phpExcelDir = __DIR__ . '/PHPExcel';

// Función para procesar un archivo
function processFile($filePath) {
    echo "Procesando archivo: $filePath\n";
    
    // Leer el contenido del archivo
    $content = file_get_contents($filePath);
    
    // Verificar si el archivo contiene la sintaxis de llaves
    if (strpos($content, '{0}') === false && !preg_match('/\$[a-zA-Z0-9_]+{[0-9]+}/', $content)) {
        echo "  No se encontró sintaxis de llaves en este archivo.\n";
        return;
    }
    
    // Reemplazar la sintaxis de llaves por la sintaxis de corchetes
    $newContent = preg_replace('/(\$[a-zA-Z0-9_]+){([0-9]+)}/', '$1[$2]', $content);
    
    // Guardar el archivo modificado
    if ($content !== $newContent) {
        file_put_contents($filePath, $newContent);
        echo "  Archivo modificado correctamente.\n";
    } else {
        echo "  No se realizaron cambios en el archivo.\n";
    }
}

// Función para recorrer un directorio recursivamente
function processDirectory($dir) {
    $files = scandir($dir);
    
    foreach ($files as $file) {
        if ($file === '.' || $file === '..') {
            continue;
        }
        
        $path = $dir . '/' . $file;
        
        if (is_dir($path)) {
            processDirectory($path);
        } elseif (pathinfo($path, PATHINFO_EXTENSION) === 'php') {
            processFile($path);
        }
    }
}

// Procesar el directorio de PHPExcel
echo "Iniciando corrección de archivos PHPExcel...\n";
processDirectory($phpExcelDir);
echo "Proceso completado.\n";
?>
