<?php
// Verificar extensiones de Oracle
echo "<h1>Verificación de configuración de Oracle</h1>";

// Verificar extensiones
echo "<h2>Extensiones PHP</h2>";
echo "<ul>";
echo "<li>Extensión OCI8: " . (extension_loaded('oci8') ? '<span style="color:green">Instalada</span>' : '<span style="color:red">No instalada</span>') . "</li>";
echo "<li>Extensión PDO_OCI: " . (extension_loaded('pdo_oci') ? '<span style="color:green">Instalada</span>' : '<span style="color:red">No instalada</span>') . "</li>";
echo "</ul>";

// Verificar variables de entorno
echo "<h2>Variables de entorno</h2>";
echo "<ul>";
echo "<li>ORACLE_HOME: " . (getenv('ORACLE_HOME') ? getenv('ORACLE_HOME') : '<span style="color:red">No definida</span>') . "</li>";
echo "<li>TNS_ADMIN: " . (getenv('TNS_ADMIN') ? getenv('TNS_ADMIN') : '<span style="color:red">No definida</span>') . "</li>";
echo "<li>LD_LIBRARY_PATH: " . (getenv('LD_LIBRARY_PATH') ? getenv('LD_LIBRARY_PATH') : '<span style="color:red">No definida</span>') . "</li>";
echo "</ul>";

// Verificar archivos de configuración
echo "<h2>Archivos de configuración</h2>";
echo "<ul>";
echo "<li>tnsnames.ora: " . (file_exists('tnsnames.ora') ? '<span style="color:green">Existe</span>' : '<span style="color:red">No existe</span>') . "</li>";
echo "<li>sqlnet.ora: " . (file_exists('sqlnet.ora') ? '<span style="color:green">Existe</span>' : '<span style="color:red">No existe</span>') . "</li>";
echo "</ul>";

// Verificar información de PHP
echo "<h2>Información de PHP</h2>";
echo "<ul>";
echo "<li>Versión de PHP: " . phpversion() . "</li>";
echo "<li>Extensiones cargadas: " . implode(', ', get_loaded_extensions()) . "</li>";
echo "</ul>";

// Verificar configuración de PDO
echo "<h2>Controladores PDO disponibles</h2>";
echo "<ul>";
$drivers = PDO::getAvailableDrivers();
foreach ($drivers as $driver) {
    echo "<li>$driver</li>";
}
echo "</ul>";

// Sugerencias para solucionar problemas
echo "<h2>Sugerencias para solucionar problemas</h2>";
echo "<ol>";
echo "<li>Asegúrate de que el cliente Oracle esté instalado en el servidor.</li>";
echo "<li>Verifica que las extensiones OCI8 y PDO_OCI estén habilitadas en php.ini.</li>";
echo "<li>Comprueba que las variables de entorno ORACLE_HOME y TNS_ADMIN estén configuradas correctamente.</li>";
echo "<li>Verifica que el archivo tnsnames.ora esté correctamente configurado y ubicado en el directorio TNS_ADMIN.</li>";
echo "<li>Asegúrate de que el servicio Oracle esté en ejecución en el servidor de base de datos.</li>";
echo "<li>Comprueba que el listener de Oracle esté configurado correctamente.</li>";
echo "<li>Verifica que el firewall no esté bloqueando la conexión.</li>";
echo "</ol>";

// Verificar conexión TNSPING (si está disponible)
echo "<h2>Verificación de conexión TNSPING</h2>";
echo "<p>Si tienes acceso al servidor de base de datos, puedes ejecutar el siguiente comando para verificar la conexión:</p>";
echo "<pre>tnsping DESA920</pre>";
echo "<p>Si el comando no está disponible en este servidor, deberás ejecutarlo en el servidor de base de datos.</p>";

// Verificar conexión con sqlplus (si está disponible)
echo "<h2>Verificación de conexión SQLPlus</h2>";
echo "<p>Si tienes acceso al servidor de base de datos, puedes ejecutar el siguiente comando para verificar la conexión:</p>";
echo "<pre>sqlplus EXP5/E*x20_22.@DESA920</pre>";
echo "<p>Si el comando no está disponible en este servidor, deberás ejecutarlo en el servidor de base de datos.</p>";

// Verificar configuración de PHP
echo "<h2>Configuración de PHP</h2>";
echo "<p>Verifica que las siguientes líneas estén en tu archivo php.ini:</p>";
echo "<pre>";
echo "extension=oci8\n";
echo "extension=pdo_oci\n";
echo "</pre>";
echo "<p>Ubicación del archivo php.ini: " . php_ini_loaded_file() . "</p>";

// Verificar configuración de Oracle Instant Client
echo "<h2>Configuración de Oracle Instant Client</h2>";
echo "<p>Si estás usando Oracle Instant Client, asegúrate de que esté correctamente instalado y configurado:</p>";
echo "<ol>";
echo "<li>Descarga Oracle Instant Client desde el sitio web de Oracle.</li>";
echo "<li>Extrae los archivos en un directorio (por ejemplo, C:\\instantclient_19_3).</li>";
echo "<li>Agrega el directorio a la variable de entorno PATH.</li>";
echo "<li>Crea un archivo tnsnames.ora en el directorio network/admin dentro del directorio de Instant Client.</li>";
echo "<li>Configura la variable de entorno TNS_ADMIN para que apunte al directorio que contiene tnsnames.ora.</li>";
echo "</ol>";

// Verificar configuración de conexión
echo "<h2>Configuración de conexión actual</h2>";
echo "<p>Archivo conexion.php:</p>";
echo "<pre>";
echo htmlspecialchars(file_get_contents('conexion.php'));
echo "</pre>";
?>
