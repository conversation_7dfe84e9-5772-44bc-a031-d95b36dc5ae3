<?php
/**
 * Visor de logs del sistema de reportes
 * 
 * Este archivo permite visualizar y buscar en los logs del sistema
 * de manera fácil y organizada.
 */

// Incluir archivos necesarios
require_once 'auth.php';
require_once 'config_logs.php';

// Verificar que el usuario esté autenticado y sea administrador
requireLogin();
if ($_SESSION['user_rol'] !== 'admin') {
    $_SESSION['error_permiso'] = 'Solo los administradores pueden ver los logs del sistema';
    header('Location: index.php');
    exit;
}

// Procesar formularios
$search_term = isset($_GET['search']) ? trim($_GET['search']) : '';
$lines_to_show = isset($_GET['lines']) ? (int)$_GET['lines'] : 100;
$action = isset($_GET['action']) ? $_GET['action'] : 'view';

// Validar número de líneas
if ($lines_to_show < 10) $lines_to_show = 10;
if ($lines_to_show > 1000) $lines_to_show = 1000;

// Obtener logs
if (!empty($search_term)) {
    $log_lines = searchLogs($search_term, $lines_to_show);
    $title = "Resultados de búsqueda para: '$search_term'";
} else {
    $log_lines = getLastLogLines($lines_to_show);
    $title = "Últimas $lines_to_show líneas del log";
}

// Acción para limpiar logs
if ($action === 'clear' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    global $log_file;
    if (file_exists($log_file)) {
        // Crear backup antes de limpiar
        $backup_file = $log_file . '.backup.' . date('Y-m-d_H-i-s');
        copy($log_file, $backup_file);
        
        // Limpiar el archivo
        file_put_contents($log_file, '');
        writeLog('Log file manually cleared by admin: ' . $_SESSION['user_username'], 'INFO', 'ADMIN');
        
        $message = "Logs limpiados correctamente. Backup creado: " . basename($backup_file);
    }
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visor de Logs - Sistema de Reportes</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .log-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .log-line {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 2px 0;
            padding: 3px 5px;
            border-radius: 3px;
            word-wrap: break-word;
        }
        
        .log-line.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .log-line.warning {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .log-line.info {
            background-color: #d4edda;
            color: #155724;
        }
        
        .log-line.debug {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .search-form {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .form-group label {
            font-weight: bold;
            color: #495057;
        }
        
        .form-group input, .form-group select {
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .stats {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <?php include 'header_with_user.php'; ?>

    <nav>
        <div class="container">
            <ul>
                <li><a href="index.php">Inicio</a></li>
                <li><a href="reportes.php">Reportes</a></li>
                <li><a href="admin_usuarios.php">Administrar Usuarios</a></li>
                <li><a href="admin_permisos_usuario.php">Administrar Permisos</a></li>
                <li><a href="ver_logs.php" class="active">Ver Logs</a></li>
                <li><a href="logout.php">Cerrar Sesión</a></li>
            </ul>
        </div>
    </nav>

    <main class="container">
        <h2>Visor de Logs del Sistema</h2>

        <?php if (isset($message)): ?>
            <div class="success" style="background-color: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin-bottom: 20px;">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <!-- Formulario de búsqueda y configuración -->
        <div class="search-form">
            <h3>Configuración de visualización</h3>
            
            <form method="get">
                <div class="form-row">
                    <div class="form-group">
                        <label for="search">Buscar en logs:</label>
                        <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search_term); ?>" 
                               placeholder="Buscar por usuario, IP, mensaje, etc.">
                    </div>
                    
                    <div class="form-group">
                        <label for="lines">Líneas a mostrar:</label>
                        <select id="lines" name="lines">
                            <option value="50" <?php echo $lines_to_show == 50 ? 'selected' : ''; ?>>50</option>
                            <option value="100" <?php echo $lines_to_show == 100 ? 'selected' : ''; ?>>100</option>
                            <option value="200" <?php echo $lines_to_show == 200 ? 'selected' : ''; ?>>200</option>
                            <option value="500" <?php echo $lines_to_show == 500 ? 'selected' : ''; ?>>500</option>
                            <option value="1000" <?php echo $lines_to_show == 1000 ? 'selected' : ''; ?>>1000</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="submit" class="btn">Buscar/Actualizar</button>
                    </div>
                </div>
            </form>
            
            <div class="btn-group">
                <a href="ver_logs.php" class="btn">Ver todos los logs</a>
                <form method="post" style="display: inline;" onsubmit="return confirm('¿Estás seguro de que quieres limpiar todos los logs? Se creará un backup automáticamente.');">
                    <input type="hidden" name="action" value="clear">
                    <button type="submit" class="btn-danger">Limpiar Logs</button>
                </form>
            </div>
        </div>

        <!-- Estadísticas -->
        <div class="stats">
            <strong>Estadísticas:</strong>
            <?php
            global $log_file;
            if (file_exists($log_file)) {
                $file_size = filesize($log_file);
                $file_size_mb = round($file_size / 1024 / 1024, 2);
                $total_lines = count(file($log_file));
                echo "Archivo: " . basename($log_file) . " | ";
                echo "Tamaño: {$file_size_mb} MB | ";
                echo "Total de líneas: {$total_lines} | ";
                echo "Mostrando: " . count($log_lines) . " líneas";
            } else {
                echo "No se encontró el archivo de logs";
            }
            ?>
        </div>

        <!-- Logs -->
        <div class="log-container">
            <h3><?php echo $title; ?></h3>
            
            <?php if (empty($log_lines)): ?>
                <p style="color: #666; font-style: italic;">No se encontraron logs.</p>
            <?php else: ?>
                <?php foreach ($log_lines as $line): ?>
                    <?php
                    // Determinar el tipo de log para aplicar estilos
                    $class = 'log-line';
                    if (strpos($line, '[ERROR]') !== false) {
                        $class .= ' error';
                    } elseif (strpos($line, '[WARNING]') !== false) {
                        $class .= ' warning';
                    } elseif (strpos($line, '[INFO]') !== false) {
                        $class .= ' info';
                    } elseif (strpos($line, '[DEBUG]') !== false) {
                        $class .= ' debug';
                    }
                    
                    // Resaltar término de búsqueda
                    $display_line = htmlspecialchars($line);
                    if (!empty($search_term)) {
                        $display_line = str_ireplace($search_term, '<mark>' . htmlspecialchars($search_term) . '</mark>', $display_line);
                    }
                    ?>
                    <div class="<?php echo $class; ?>"><?php echo $display_line; ?></div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <div style="margin-top: 20px;">
            <p><a href="index.php" class="btn">Volver al Inicio</a></p>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Sistema de Reportes de Sueldos. Todos los derechos reservados.</p>
        </div>
    </footer>

    <script>
        // Auto-refresh cada 30 segundos si no hay término de búsqueda
        <?php if (empty($search_term)): ?>
        setTimeout(function() {
            window.location.reload();
        }, 30000);
        <?php endif; ?>
    </script>
</body>
</html>
