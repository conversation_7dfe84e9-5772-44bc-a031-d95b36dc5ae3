<?php
// Incluir archivo de conexión y permisos
require_once 'conexion.php';
require_once 'permisos.php';

try {
    $pdo = getConexion();
    
    echo "<h2>Actualización de Permisos</h2>";
    echo "<p>Este script actualiza el permiso 'ver_consulta_hidraulica' a 'ver_reporte_hidraulica' para todos los usuarios.</p>";
    
    // Obtener todos los usuarios con permisos específicos
    $sql = "SELECT id_usuario, permisos FROM permisos_usuario";
    $stmt = $pdo->query($sql);
    
    $usuariosActualizados = 0;
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $userId = $row['ID_USUARIO'];
        $permisos = $row['PERMISOS'];
        
        // Verificar si el usuario tiene el permiso antiguo
        if (strpos($permisos, 'ver_consulta_hidraulica') !== false) {
            // Obtener el nombre del usuario
            $sqlUser = "SELECT username FROM usuarios WHERE id = :id";
            $stmtUser = $pdo->prepare($sqlUser);
            $stmtUser->bindParam(':id', $userId);
            $stmtUser->execute();
            $username = $stmtUser->fetchColumn();
            
            echo "<p>Usuario $username (ID: $userId) tiene el permiso antiguo 'ver_consulta_hidraulica'.</p>";
            
            // Reemplazar el permiso antiguo por el nuevo
            $nuevosPermisos = str_replace('ver_consulta_hidraulica', 'ver_reporte_hidraulica', $permisos);
            
            // Si el permiso nuevo ya está en la lista, eliminar el permiso antiguo
            if (strpos($permisos, 'ver_reporte_hidraulica') !== false) {
                $nuevosPermisos = str_replace('ver_consulta_hidraulica', '', $permisos);
                $nuevosPermisos = str_replace(',,', ',', $nuevosPermisos);
                $nuevosPermisos = trim($nuevosPermisos, ',');
            }
            
            // Actualizar los permisos del usuario
            $sqlUpdate = "UPDATE permisos_usuario SET permisos = :permisos WHERE id_usuario = :id_usuario";
            $stmtUpdate = $pdo->prepare($sqlUpdate);
            $stmtUpdate->bindParam(':permisos', $nuevosPermisos);
            $stmtUpdate->bindParam(':id_usuario', $userId);
            
            if ($stmtUpdate->execute()) {
                echo "<p style='color: green;'>Permisos actualizados para el usuario $username (ID: $userId).</p>";
                echo "<p>Permisos antiguos: $permisos</p>";
                echo "<p>Permisos nuevos: $nuevosPermisos</p>";
                $usuariosActualizados++;
            } else {
                echo "<p style='color: red;'>Error al actualizar los permisos para el usuario $username (ID: $userId).</p>";
            }
        }
    }
    
    echo "<h3>Resumen</h3>";
    echo "<p>Usuarios actualizados: $usuariosActualizados</p>";
    
    // Limpiar la caché de permisos
    if (isset($_SESSION['permisos_cache'])) {
        unset($_SESSION['permisos_cache']);
        echo "<p>Caché de permisos limpiada correctamente.</p>";
    }
    
    echo "<p><a href='admin_permisos_usuario.php'>Volver a la administración de permisos</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
