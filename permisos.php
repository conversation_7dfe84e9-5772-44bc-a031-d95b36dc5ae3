<?php
// Incluir archivo de autenticación
require_once 'auth.php';

/**
 * Define los permisos disponibles en el sistema
 * @return array
 */
function getPermisos() {
    return [
        // Permisos generales
        'ver_reportes' => 'Ver todos los reportes',
        'exportar_excel' => 'Exportar datos a Excel',
        'admin_usuarios' => 'Administrar usuarios',

        // Permisos para reportes específicos - Sin Ajuste
        'ver_reporte_falla_caja' => 'Ver reporte de Falla de Caja',
        'ver_reporte_san_nicolas' => 'Ver reporte de San Nicolás Mensual',
        'ver_reporte_santisimo_sacramento' => 'Ver reporte de Santísimo Sacramento Mensual',
        'ver_reporte_universidad_catolica' => 'Ver reporte de Universidad Católica Mensual',
        'ver_reporte_poder_judicial' => 'Ver reporte de Imponible Poder Judicial',
        'ver_reporte_reparto_poder_judicial' => 'Ver reporte de Reparto Poder Judicial',

        // Permisos para reportes específicos - Con Ajuste
        'ver_reporte_hidraulica' => 'Ver reporte de Imponible Hidráulica',
        'ver_consulta_policia' => 'Ver consulta de Imponible Policía',
        'ver_reporte_resumen_centros' => 'Ver reporte de Resumen por Centros',
        'ver_reporte_resumen_sectores' => 'Ver reporte de Resumen por Sectores',
        'ver_reporte_obra_social' => 'Ver reporte de Obra Social Provincia'
    ];
}

/**
 * Define los permisos asignados a cada rol
 * @return array
 */
function getPermisosRoles() {
    return [
        'admin' => [
            // Permisos generales
            'ver_reportes',
            'exportar_excel',
            'admin_usuarios',

            // Permisos para reportes específicos - Sin Ajuste
            'ver_reporte_falla_caja',
            'ver_reporte_san_nicolas',
            'ver_reporte_santisimo_sacramento',
            'ver_reporte_universidad_catolica',
            'ver_reporte_poder_judicial',
            'ver_reporte_reparto_poder_judicial',

            // Permisos para reportes específicos - Con Ajuste
            'ver_reporte_hidraulica',
            'ver_consulta_policia',
            'ver_reporte_resumen_centros',
            'ver_reporte_resumen_sectores',
            'ver_reporte_obra_social'
        ],
        'usuario' => [
            'exportar_excel',
            'ver_reporte_falla_caja',
            'ver_reporte_san_nicolas',
            'ver_reporte_santisimo_sacramento',
            'ver_reporte_universidad_catolica',
            'ver_reporte_poder_judicial'
            ,
            // Los usuarios no tienen permisos por defecto, se asignan individualmente
        ]
    ];
}

/**
 * Obtiene los permisos de un usuario específico
 * @param int $userId
 * @param bool $forceRefresh Si es true, fuerza la recarga de permisos desde la base de datos
 * @return array
 */
function getPermisosUsuario($userId, $forceRefresh = false) {
    $username = isset($_SESSION['user_username']) ? $_SESSION['user_username'] : 'desconocido';
    $rol = isset($_SESSION['user_rol']) ? $_SESSION['user_rol'] : 'desconocido';

    error_log("Obteniendo permisos para usuario $username (ID: $userId, Rol: $rol)");

    // Si es administrador, devolver todos los permisos disponibles
    if ($rol === 'admin') {
        $todosPermisos = array_keys(getPermisos());
        error_log("Usuario $username es administrador, tiene todos los permisos: " . print_r($todosPermisos, true));
        return $todosPermisos;
    }

    try {
        $pdo = getConexion();
        $permisosArray = [];

        // Verificar si hay permisos específicos para este usuario
        $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id_usuario', $userId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            // Los permisos se almacenan como una cadena separada por comas
            $permisos = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];
            error_log("Permisos encontrados en la base de datos para $username: $permisos");

            if (!empty($permisos)) {
                $permisosArray = explode(',', $permisos);

                // Limpiar los permisos (eliminar espacios en blanco)
                $permisosArray = array_map('trim', $permisosArray);

                // Eliminar permisos vacíos
                $permisosArray = array_filter($permisosArray, function($p) {
                    return !empty($p);
                });

                // Agregar mensaje de depuración
                error_log("Permisos específicos para usuario $username: " . print_r($permisosArray, true));
            }
        } else {
            error_log("No se encontraron registros de permisos específicos para el usuario $username");
        }

        // Agregar permisos del rol
        $permisosRoles = getPermisosRoles();
        if (isset($permisosRoles[$rol])) {
            foreach ($permisosRoles[$rol] as $permiso) {
                if (!in_array($permiso, $permisosArray)) {
                    $permisosArray[] = $permiso;
                }
            }
            error_log("Permisos del rol $rol agregados para $username: " . print_r($permisosRoles[$rol], true));
        }

        // Asegurarse de que el usuario tenga el permiso de exportar_excel
        if (!in_array('exportar_excel', $permisosArray)) {
            $permisosArray[] = 'exportar_excel';
        }

        error_log("Permisos finales para usuario $username: " . print_r($permisosArray, true));
        return $permisosArray;
    } catch (PDOException $e) {
        error_log("Error en getPermisosUsuario: " . $e->getMessage());

        // En caso de error, devolver los permisos del rol
        $permisosRoles = getPermisosRoles();
        if (isset($permisosRoles[$rol])) {
            error_log("Devolviendo permisos del rol $rol para $username debido a error: " . print_r($permisosRoles[$rol], true));
            return $permisosRoles[$rol];
        }

        return [];
    }
}

/**
 * Guarda los permisos de un usuario específico
 * @param int $userId
 * @param array $permisos
 * @return bool|string
 */
function guardarPermisosUsuario($userId, $permisos) {
    try {
        $pdo = getConexion();
        $username = isset($_SESSION['user_username']) ? $_SESSION['user_username'] : 'desconocido';

        error_log("Guardando permisos para usuario $userId: " . print_r($permisos, true));

        // Verificar que el usuario exista
        $sql = "SELECT username FROM usuarios WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            $errorMsg = "Error: El usuario con ID $userId no existe";
            error_log($errorMsg);

            // Verificar si es dpohidraulica y buscar su ID correcto
            if (isset($_GET['user_id']) && $_GET['user_id'] == 2 && isset($_GET['username']) && $_GET['username'] == 'dpohidraulica') {
                // Buscar el ID correcto de dpohidraulica
                $sql = "SELECT id FROM usuarios WHERE username = 'dpohidraulica'";
                $stmt = $pdo->query($sql);

                if ($stmt->rowCount() > 0) {
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    $correctId = isset($result['ID']) ? $result['ID'] : $result['id'];
                    error_log("El usuario dpohidraulica existe con ID: $correctId, no con ID: $userId");

                    // Usar el ID correcto
                    $userId = $correctId;
                    error_log("Usando ID correcto: $userId para dpohidraulica");
                } else {
                    error_log("El usuario dpohidraulica no existe en la base de datos");
                    return $errorMsg . " y el usuario dpohidraulica no existe en la base de datos";
                }
            } else {
                return $errorMsg;
            }
        }

        // Obtener el nombre de usuario
        $sql = "SELECT username FROM usuarios WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $userId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $userRow = $stmt->fetch(PDO::FETCH_ASSOC);
            $targetUsername = isset($userRow['USERNAME']) ? $userRow['USERNAME'] : $userRow['username'];
            error_log("Guardando permisos para usuario: $targetUsername (ID: $userId)");
        } else {
            error_log("No se pudo obtener el nombre de usuario para ID: $userId");
            $targetUsername = "usuario_$userId";
        }

        // Si no hay permisos, usar un array vacío
        if (!is_array($permisos)) {
            $permisos = [];
            error_log("Permisos no es un array, usando array vacío");
        }

        // Limpiar los permisos (eliminar espacios en blanco)
        $permisos = array_map('trim', $permisos);

        // Eliminar permisos vacíos
        $permisos = array_filter($permisos, function($p) {
            return !empty($p);
        });

        // Asegurarse de que el usuario tenga el permiso de exportar_excel
        if (!in_array('exportar_excel', $permisos)) {
            $permisos[] = 'exportar_excel';
        }

        // Caso especial para dpohidraulica
        if ($targetUsername === 'dpohidraulica' && !in_array('ver_reporte_hidraulica', $permisos)) {
            $permisos[] = 'ver_reporte_hidraulica';
            error_log("Agregando permiso ver_reporte_hidraulica para usuario dpohidraulica");
        }

        // Convertir el array de permisos a una cadena separada por comas
        $permisosStr = implode(',', $permisos);
        error_log("Permisos como cadena: $permisosStr");

        // Verificar si la tabla existe
        try {
            $sql = "SELECT 1 FROM permisos_usuario WHERE ROWNUM = 1";
            $pdo->query($sql);
            error_log("Tabla permisos_usuario existe");
        } catch (PDOException $e) {
            // La tabla no existe, ejecutamos el script para crearla
            error_log("Tabla permisos_usuario no existe, ejecutando script para crearla");
            try {
                // Crear la tabla manualmente
                $sql = "CREATE TABLE permisos_usuario (
                    id_usuario NUMBER(10) NOT NULL,
                    permisos VARCHAR2(4000),
                    CONSTRAINT pk_permisos_usuario PRIMARY KEY (id_usuario),
                    CONSTRAINT fk_permisos_usuario FOREIGN KEY (id_usuario) REFERENCES usuarios(id)
                )";
                $pdo->exec($sql);
                error_log("Tabla permisos_usuario creada manualmente");
            } catch (Exception $e2) {
                // Intentar con el script
                try {
                    include 'crear_tabla_permisos.php';
                    error_log("Script crear_tabla_permisos.php ejecutado");
                } catch (Exception $e3) {
                    $error = "Error al crear la tabla de permisos: " . $e3->getMessage();
                    error_log($error);
                    return $error;
                }
            }
        }

        // Verificar si ya existen permisos para este usuario
        $sql = "SELECT COUNT(*) FROM permisos_usuario WHERE id_usuario = :id_usuario";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id_usuario', $userId);
        $stmt->execute();

        $count = $stmt->fetchColumn();
        error_log("Permisos existentes para usuario ID $userId: $count");

        if ($count > 0) {
            // Actualizar permisos existentes
            $sql = "UPDATE permisos_usuario SET permisos = :permisos WHERE id_usuario = :id_usuario";
            error_log("Actualizando permisos existentes para usuario ID $userId");
        } else {
            // Insertar nuevos permisos
            $sql = "INSERT INTO permisos_usuario (id_usuario, permisos) VALUES (:id_usuario, :permisos)";
            error_log("Insertando nuevos permisos para usuario ID $userId");
        }

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id_usuario', $userId);
        $stmt->bindParam(':permisos', $permisosStr);

        $result = $stmt->execute();
        if ($result) {
            error_log("Permisos guardados correctamente para usuario ID $userId");

            // Verificar que los permisos se hayan guardado correctamente
            $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':id_usuario', $userId);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $permisosGuardados = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];
                error_log("Permisos guardados en la base de datos para usuario ID $userId: $permisosGuardados");

                // Limpiar la caché de permisos para este usuario
                if (isset($_SESSION['permisos_cache'][$userId])) {
                    unset($_SESSION['permisos_cache'][$userId]);
                    error_log("Caché de permisos limpiada para usuario ID $userId");
                }

                // Limpiar toda la caché de permisos
                if (isset($_SESSION['permisos_cache'])) {
                    unset($_SESSION['permisos_cache']);
                    error_log("Toda la caché de permisos ha sido limpiada");
                }
            } else {
                error_log("No se encontraron permisos guardados para el usuario ID $userId después de guardarlos");
            }

            return true;
        } else {
            $error = $stmt->errorInfo();
            $errorMsg = "Error al guardar los permisos: " . $error[2];
            error_log($errorMsg);
            return $errorMsg;
        }
    } catch (PDOException $e) {
        $errorMsg = "Error en la base de datos: " . $e->getMessage();
        error_log("Error en guardarPermisosUsuario: " . $e->getMessage());
        return $errorMsg;
    }
}

/**
 * Verifica si un usuario tiene un permiso específico
 * @param string $permiso
 * @param bool $forceRefresh Si es true, fuerza la recarga de permisos desde la base de datos
 * @return bool
 */
function tienePermiso($permiso, $forceRefresh = false) {
    // Si no está autenticado, no tiene permisos
    if (!isLoggedIn()) {
        error_log("Usuario no autenticado, no tiene permisos");
        return false;
    }

    $userId = $_SESSION['user_id'];
    $username = $_SESSION['user_username'];
    $rol = $_SESSION['user_rol'];

    error_log("Verificando permiso '$permiso' para usuario $username (ID: $userId, Rol: $rol)");

    // Los administradores tienen todos los permisos
    if ($rol === 'admin') {
        error_log("Usuario $username es administrador, tiene todos los permisos");
        return true;
    }

    // Caso especial para dpohidraulica
    if ($username === 'dpohidraulica' && ($permiso === 'ver_reporte_hidraulica' || $permiso === 'ver_consulta_hidraulica')) {
        error_log("Usuario dpohidraulica tiene acceso especial al reporte de hidráulica");
        return true;
    }

    // Obtener permisos directamente de la base de datos para evitar problemas de caché
    try {
        $pdo = getConexion();

        // Verificar si hay permisos específicos para este usuario
        $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id_usuario', $userId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $permisos = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];

            if (!empty($permisos)) {
                $permisosArray = explode(',', $permisos);
                $permisosArray = array_map('trim', $permisosArray);

                error_log("Permisos del usuario $username desde DB: " . print_r($permisosArray, true));

                // Verificar el permiso específico
                if (in_array($permiso, $permisosArray)) {
                    error_log("Usuario $username tiene el permiso específico '$permiso' en la DB");
                    return true;
                }

                // Si el permiso es para ver reportes, verificar si tiene algún permiso de reporte
                if ($permiso === 'ver_reportes') {
                    foreach ($permisosArray as $p) {
                        if (strpos($p, 'ver_reporte_') === 0 || strpos($p, 'ver_consulta_') === 0) {
                            error_log("Usuario $username tiene permiso específico para un reporte ($p), por lo que puede acceder a la página de reportes");
                            return true;
                        }
                    }
                }

                // Si el permiso es para un reporte específico, verificar si tiene permiso general para ver reportes
                if ((strpos($permiso, 'ver_reporte_') === 0 || strpos($permiso, 'ver_consulta_') === 0) && in_array('ver_reportes', $permisosArray)) {
                    error_log("Usuario $username tiene permiso general 'ver_reportes', por lo que puede ver el reporte '$permiso'");
                    return true;
                }

                // Compatibilidad con el nombre anterior del permiso
                if ($permiso === 'ver_consulta_hidraulica' && in_array('ver_reporte_hidraulica', $permisosArray)) {
                    error_log("Usuario $username tiene permiso 'ver_reporte_hidraulica', por lo que puede ver 'ver_consulta_hidraulica'");
                    return true;
                }

                if ($permiso === 'ver_reporte_hidraulica' && in_array('ver_consulta_hidraulica', $permisosArray)) {
                    error_log("Usuario $username tiene permiso 'ver_consulta_hidraulica', por lo que puede ver 'ver_reporte_hidraulica'");
                    return true;
                }
            }
        }

        // Si no hay permisos específicos, verificar los permisos del rol
        $permisosRoles = getPermisosRoles();
        if (isset($permisosRoles[$rol]) && in_array($permiso, $permisosRoles[$rol])) {
            error_log("Usuario $username tiene el permiso '$permiso' por su rol '$rol'");
            return true;
        }

        // Verificaciones adicionales para compatibilidad
        if ($rol === 'usuario') {
            // Los usuarios siempre pueden exportar a Excel
            if ($permiso === 'exportar_excel') {
                return true;
            }

            // Si el usuario tiene algún permiso específico para reportes, puede acceder a la página de reportes
            if ($permiso === 'ver_reportes') {
                $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
                $stmt = $pdo->prepare($sql);
                $stmt->bindParam(':id_usuario', $userId);
                $stmt->execute();

                if ($stmt->rowCount() > 0) {
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    $permisos = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];

                    if (!empty($permisos)) {
                        $permisosArray = explode(',', $permisos);
                        foreach ($permisosArray as $p) {
                            if (strpos($p, 'ver_reporte_') === 0 || strpos($p, 'ver_consulta_') === 0) {
                                error_log("Usuario $username tiene permiso específico para un reporte ($p), por lo que puede acceder a la página de reportes");
                                return true;
                            }
                        }
                    }
                }
            }
        }

    } catch (PDOException $e) {
        error_log("Error al verificar permisos en la base de datos: " . $e->getMessage());
    }

    error_log("Usuario $username NO tiene el permiso '$permiso'");
    return false;
}

/**
 * Redirige al usuario a la página principal si no tiene el permiso especificado
 * @param string $permiso
 */
function requirePermiso($permiso) {
    if (!tienePermiso($permiso)) {
        // Guardar mensaje de error en la sesión
        $_SESSION['error_permiso'] = 'No tienes permiso para acceder a esta página';
        header('Location: index.php');
        exit;
    }
}

/**
 * Obtiene los permisos de un rol específico
 * @param string $rol
 * @return array
 */
function getPermisosRol($rol) {
    $permisosRoles = getPermisosRoles();

    if (isset($permisosRoles[$rol])) {
        return $permisosRoles[$rol];
    }

    return [];
}

/**
 * Actualiza los permisos de un rol
 * @param string $rol
 * @param array $permisos
 * @return bool
 */
function actualizarPermisosRol($rol, $permisos) {
    // Esta función sería para una implementación más avanzada
    // donde los permisos se guardan en la base de datos
    // Por ahora, solo devolvemos true
    return true;
}
?>
