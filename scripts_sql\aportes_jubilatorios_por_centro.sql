-- Reporte de Aportes Jubilatorios por Centro
-- Este reporte muestra los aportes jubilatorios agrupados por centro
-- Tablespace: SUELDOS27+

SELECT 
    a.CENTRO as "CENTRO",
    COUNT(a.PADROND) as "CANTIDAD_EMPLEADOS",
    SUM(APORJUB) as "TOTAL_APORTES_JUB",
    AVG(APORJUB) as "PROMEDIO_APORTES_JUB",
    MIN(APORJUB) as "MINIMO_APORTE_JUB",
    MAX(APORJUB) as "MAXIMO_APORTE_JUB",
    SUM(a.TACAREDU) as "TOTAL_IMPONIBLE",
    AVG(a.TACAREDU) as "PROMEDIO_IMPONIBLE",
    ROUND(SUM(APORJUB) / NULLIF(SUM(a.TACAREDU), 0) * 100, 2) as "PORCENTAJE_APORTE"
FROM 
    sueldo0425 a
JOIN 
    imponi0425B b ON a.PADROND = b.PADROND
WHERE
    APORJUB > 0
GROUP BY 
    a.CENTRO
ORDER BY 
    a.CENTRO
