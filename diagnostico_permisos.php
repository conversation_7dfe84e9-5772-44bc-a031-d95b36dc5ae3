<?php
// Incluir archivos necesarios
require_once 'permisos.php';

// Verificar que el usuario esté autenticado
requireLogin();

// Obtener información del usuario actual
$userId = $_SESSION['user_id'];
$username = $_SESSION['user_username'];
$rol = $_SESSION['user_rol'];

// Obtener permisos del usuario
$permisosUsuario = getPermisosUsuario($userId, true); // Forzar recarga

// Obtener todos los permisos disponibles
$todosPermisos = getPermisos();

// Obtener permisos del rol
$permisosRol = getPermisosRol($rol);

// Verificar permisos específicos para reportes
$permisosReportes = [];
foreach ($todosPermisos as $codigo => $descripcion) {
    if (strpos($codigo, 'ver_reporte_') === 0 || strpos($codigo, 'ver_consulta_') === 0) {
        $permisosReportes[$codigo] = [
            'descripcion' => $descripcion,
            'tiene_permiso' => tienePermiso($codigo, true), // Forzar recarga
            'en_array_permisos' => in_array($codigo, $permisosUsuario),
            'en_array_rol' => in_array($codigo, $permisosRol)
        ];
    }
}

// Verificar si hay permisos en la base de datos
try {
    $pdo = getConexion();
    $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id_usuario', $userId);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $permisosDB = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];
        $permisosDBArray = explode(',', $permisosDB);
        $permisosDBArray = array_map('trim', $permisosDBArray);
    } else {
        $permisosDB = "No hay permisos en la base de datos";
        $permisosDBArray = [];
    }
} catch (PDOException $e) {
    $permisosDB = "Error al obtener permisos: " . $e->getMessage();
    $permisosDBArray = [];
}

// Verificar la tabla de permisos
try {
    $pdo = getConexion();
    $sql = "SELECT COUNT(*) FROM permisos_usuario";
    $count = $pdo->query($sql)->fetchColumn();
    $tablaExiste = true;
    $registrosTabla = $count;
} catch (PDOException $e) {
    $tablaExiste = false;
    $registrosTabla = 0;
}

// Verificar la estructura de la tabla
$estructuraTabla = [];
if ($tablaExiste) {
    try {
        $pdo = getConexion();
        $sql = "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'PERMISOS_USUARIO'";
        $stmt = $pdo->query($sql);
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $estructuraTabla[] = $row;
        }
    } catch (PDOException $e) {
        $estructuraTabla = ["Error al obtener estructura: " . $e->getMessage()];
    }
}

// Verificar todos los registros de la tabla
$registrosPermisos = [];
if ($tablaExiste) {
    try {
        $pdo = getConexion();
        $sql = "SELECT * FROM permisos_usuario";
        $stmt = $pdo->query($sql);
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $registrosPermisos[] = $row;
        }
    } catch (PDOException $e) {
        $registrosPermisos = ["Error al obtener registros: " . $e->getMessage()];
    }
}

// Verificar la caché de permisos
$cachePermisos = isset($_SESSION['permisos_cache']) ? $_SESSION['permisos_cache'] : "No hay caché de permisos";

// Verificar la sesión
$datosSession = $_SESSION;
// Eliminar información sensible
if (isset($datosSession['permisos_cache'])) {
    $datosSession['permisos_cache'] = "Existe (detalles omitidos)";
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnóstico de Permisos - Sistema de Reportes de Sueldos</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .diagnostico-container {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .seccion {
            margin-bottom: 30px;
        }
        
        .seccion h3 {
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .info-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        
        .info-item strong {
            display: inline-block;
            min-width: 200px;
        }
        
        .permiso-item {
            margin-bottom: 5px;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
        }
        
        .permiso-item.tiene {
            background-color: #d4edda;
        }
        
        .permiso-item.no-tiene {
            background-color: #f8d7da;
        }
        
        .acciones {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        table, th, td {
            border: 1px solid #ddd;
        }
        
        th, td {
            padding: 10px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>Sistema de Reportes de Sueldos</h1>
        </div>
    </header>
    
    <nav>
        <div class="container">
            <ul>
                <li><a href="index.php">Inicio</a></li>
                <li><a href="reportes.php">Reportes</a></li>
                <?php if (isAdmin()): ?>
                    <li><a href="admin_usuarios.php">Administrar Usuarios</a></li>
                    <li><a href="admin_permisos_usuario.php">Administrar Permisos</a></li>
                <?php endif; ?>
                <li><a href="logout.php">Cerrar Sesión (<?php echo htmlspecialchars($_SESSION['user_username']); ?>)</a></li>
            </ul>
        </div>
    </nav>
    
    <main class="container">
        <h2>Diagnóstico de Permisos</h2>
        
        <div class="diagnostico-container">
            <div class="seccion">
                <h3>Información del Usuario</h3>
                <div class="info-item">
                    <strong>ID:</strong> <?php echo $userId; ?>
                </div>
                <div class="info-item">
                    <strong>Usuario:</strong> <?php echo htmlspecialchars($username); ?>
                </div>
                <div class="info-item">
                    <strong>Rol:</strong> <?php echo htmlspecialchars($rol); ?>
                </div>
            </div>
            
            <div class="seccion">
                <h3>Permisos del Usuario</h3>
                <?php if (empty($permisosUsuario)): ?>
                    <p>No se encontraron permisos para este usuario.</p>
                <?php else: ?>
                    <ul>
                        <?php foreach ($permisosUsuario as $permiso): ?>
                            <li><?php echo htmlspecialchars($permiso); ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
            
            <div class="seccion">
                <h3>Permisos del Rol (<?php echo htmlspecialchars($rol); ?>)</h3>
                <?php if (empty($permisosRol)): ?>
                    <p>No se encontraron permisos para este rol.</p>
                <?php else: ?>
                    <ul>
                        <?php foreach ($permisosRol as $permiso): ?>
                            <li><?php echo htmlspecialchars($permiso); ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
            
            <div class="seccion">
                <h3>Permisos en la Base de Datos</h3>
                <div class="info-item">
                    <strong>Permisos:</strong> <?php echo htmlspecialchars($permisosDB); ?>
                </div>
                <?php if (!empty($permisosDBArray)): ?>
                    <ul>
                        <?php foreach ($permisosDBArray as $permiso): ?>
                            <li><?php echo htmlspecialchars($permiso); ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
            
            <div class="seccion">
                <h3>Verificación de Permisos para Reportes</h3>
                <table>
                    <tr>
                        <th>Código</th>
                        <th>Descripción</th>
                        <th>Tiene Permiso</th>
                        <th>En Array Permisos</th>
                        <th>En Array Rol</th>
                    </tr>
                    <?php foreach ($permisosReportes as $codigo => $info): ?>
                        <tr class="<?php echo $info['tiene_permiso'] ? 'tiene' : 'no-tiene'; ?>">
                            <td><?php echo htmlspecialchars($codigo); ?></td>
                            <td><?php echo htmlspecialchars($info['descripcion']); ?></td>
                            <td><?php echo $info['tiene_permiso'] ? 'Sí' : 'No'; ?></td>
                            <td><?php echo $info['en_array_permisos'] ? 'Sí' : 'No'; ?></td>
                            <td><?php echo $info['en_array_rol'] ? 'Sí' : 'No'; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </table>
            </div>
            
            <div class="seccion">
                <h3>Información de la Tabla de Permisos</h3>
                <div class="info-item">
                    <strong>Tabla existe:</strong> <?php echo $tablaExiste ? 'Sí' : 'No'; ?>
                </div>
                <div class="info-item">
                    <strong>Número de registros:</strong> <?php echo $registrosTabla; ?>
                </div>
                
                <?php if (!empty($estructuraTabla)): ?>
                    <h4>Estructura de la Tabla</h4>
                    <table>
                        <tr>
                            <th>Columna</th>
                            <th>Tipo</th>
                            <th>Longitud</th>
                        </tr>
                        <?php foreach ($estructuraTabla as $columna): ?>
                            <tr>
                                <td><?php echo isset($columna['COLUMN_NAME']) ? $columna['COLUMN_NAME'] : $columna['column_name']; ?></td>
                                <td><?php echo isset($columna['DATA_TYPE']) ? $columna['DATA_TYPE'] : $columna['data_type']; ?></td>
                                <td><?php echo isset($columna['DATA_LENGTH']) ? $columna['DATA_LENGTH'] : $columna['data_length']; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                <?php endif; ?>
                
                <?php if (!empty($registrosPermisos)): ?>
                    <h4>Registros de la Tabla</h4>
                    <table>
                        <tr>
                            <th>ID Usuario</th>
                            <th>Permisos</th>
                        </tr>
                        <?php foreach ($registrosPermisos as $registro): ?>
                            <tr>
                                <td><?php echo isset($registro['ID_USUARIO']) ? $registro['ID_USUARIO'] : $registro['id_usuario']; ?></td>
                                <td><?php echo isset($registro['PERMISOS']) ? $registro['PERMISOS'] : $registro['permisos']; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                <?php endif; ?>
            </div>
            
            <div class="acciones">
                <form method="post" action="limpiar_sesion.php">
                    <button type="submit" class="btn">Cerrar sesión y limpiar caché</button>
                </form>
                
                <form method="post" action="admin_permisos_usuario.php">
                    <input type="hidden" name="limpiar_cache" value="1">
                    <button type="submit" class="btn">Limpiar caché de permisos</button>
                </form>
                
                <?php if (isAdmin()): ?>
                    <a href="admin_permisos_usuario.php" class="btn">Administrar Permisos</a>
                <?php endif; ?>
            </div>
        </div>
    </main>
    
    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Sistema de Reportes de Sueldos. Todos los derechos reservados.</p>
        </div>
    </footer>
</body>
</html>
