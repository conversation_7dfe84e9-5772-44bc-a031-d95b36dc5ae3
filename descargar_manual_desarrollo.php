<?php
// Incluir archivos necesarios
require_once 'permisos.php';
require_once 'auth.php';

// Verificar si el usuario está autenticado
requireLogin();

// Verificar si el usuario es administrador
requireAdmin();

// Obtener el contenido del archivo HTML
$html = file_get_contents('manual_desarrollo.html');

// Generar nombre de archivo
$filename = 'Manual_Desarrollo_Sistema_Reportes_' . date('Ymd') . '.html';

// Configurar encabezados para descarga
header('Content-Type: text/html');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . strlen($html));
header('Cache-Control: max-age=0');
header('Pragma: public');
header('Expires: 0');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Cache-Control: private', false);

// Enviar el contenido
echo $html;
exit;
?>
