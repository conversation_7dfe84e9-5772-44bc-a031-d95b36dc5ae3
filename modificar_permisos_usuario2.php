<?php
// Incluir archivos necesarios
require_once 'conexion.php';
require_once 'permisos.php';
require_once 'auth.php';

// Verificar que el usuario esté autenticado y tenga permiso
requireLogin();
requirePermiso('admin_usuarios');

// Mensaje de éxito o error
$mensaje = '';
$error = '';

// ID del usuario a modificar
$userId = 2;

// Obtener información del usuario
try {
    $pdo = getConexion();
    $sql = "SELECT id, username, nombre, rol FROM usuarios WHERE id = :id";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $userId);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $usuario = $stmt->fetch(PDO::FETCH_ASSOC);
        $username = isset($usuario['USERNAME']) ? $usuario['USERNAME'] : $usuario['username'];
        $nombre = isset($usuario['NOMBRE']) ? $usuario['NOMBRE'] : $usuario['nombre'];
        $rol = isset($usuario['ROL']) ? $usuario['ROL'] : $usuario['rol'];
        
        // Obtener permisos actuales
        $permisosActuales = getPermisosUsuario($userId, true);
        
        // Permisos que se deben mantener (básicos para un usuario)
        $permisosBasicos = [
            'ver_reportes',
            'exportar_excel',
            'ver_reporte_hidraulica'
        ];
        
        // Permisos a eliminar (todos los demás)
        $permisosAEliminar = array_diff($permisosActuales, $permisosBasicos);
        
        // Verificar si se ha enviado el formulario
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (isset($_POST['eliminar_permisos'])) {
                // Guardar solo los permisos básicos
                $resultado = guardarPermisosUsuario($userId, $permisosBasicos);
                
                if ($resultado === true) {
                    $mensaje = "Permisos modificados correctamente para el usuario $username (ID: $userId)";
                    
                    // Actualizar permisos actuales
                    $permisosActuales = getPermisosUsuario($userId, true);
                    $permisosAEliminar = array_diff($permisosActuales, $permisosBasicos);
                    
                    // Limpiar la caché de permisos
                    if (isset($_SESSION['permisos_cache'])) {
                        unset($_SESSION['permisos_cache']);
                    }
                } else {
                    $error = "Error al modificar permisos: $resultado";
                }
            } elseif (isset($_POST['permisos_personalizados'])) {
                // Obtener los permisos seleccionados
                $permisosSeleccionados = isset($_POST['permisos']) ? $_POST['permisos'] : [];
                
                // Asegurarse de que los permisos básicos estén incluidos
                $permisosSeleccionados = array_unique(array_merge($permisosSeleccionados, $permisosBasicos));
                
                // Guardar los permisos seleccionados
                $resultado = guardarPermisosUsuario($userId, $permisosSeleccionados);
                
                if ($resultado === true) {
                    $mensaje = "Permisos personalizados guardados correctamente para el usuario $username (ID: $userId)";
                    
                    // Actualizar permisos actuales
                    $permisosActuales = getPermisosUsuario($userId, true);
                    $permisosAEliminar = array_diff($permisosActuales, $permisosBasicos);
                    
                    // Limpiar la caché de permisos
                    if (isset($_SESSION['permisos_cache'])) {
                        unset($_SESSION['permisos_cache']);
                    }
                } else {
                    $error = "Error al guardar permisos personalizados: $resultado";
                }
            }
        }
    } else {
        $error = "El usuario con ID $userId no existe";
    }
} catch (PDOException $e) {
    $error = "Error en la base de datos: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <?php include 'head_common.php'; ?>
    <title>Modificar Permisos de Usuario - Sistema de Reportes de Sueldos</title>
    <style>
        .permisos-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        
        .permisos-actuales, .permisos-a-eliminar {
            flex: 1;
            min-width: 300px;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
        }
        
        .permiso-item {
            margin-bottom: 10px;
            padding: 5px;
            border-radius: 3px;
        }
        
        .permiso-basico {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        
        .permiso-eliminar {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        
        .acciones {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
    </style>
</head>
<body>
<?php include 'header_with_user.php'; ?>

    <?php include 'nav_menu.php'; ?>

    <main class="container">
        <h2>Modificar Permisos de Usuario</h2>
        
        <?php if (!empty($mensaje)): ?>
            <div class="success"><?php echo $mensaje; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (isset($username)): ?>
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                <h3>Información del Usuario</h3>
                <p><strong>ID:</strong> <?php echo $userId; ?></p>
                <p><strong>Usuario:</strong> <?php echo htmlspecialchars($username); ?></p>
                <p><strong>Nombre:</strong> <?php echo htmlspecialchars($nombre); ?></p>
                <p><strong>Rol:</strong> <?php echo htmlspecialchars($rol); ?></p>
            </div>
            
            <div class="permisos-container">
                <div class="permisos-actuales">
                    <h3>Permisos Actuales</h3>
                    <?php if (!empty($permisosActuales)): ?>
                        <form method="post">
                            <?php foreach ($permisosActuales as $permiso): ?>
                                <div class="permiso-item <?php echo in_array($permiso, $permisosBasicos) ? 'permiso-basico' : 'permiso-eliminar'; ?>">
                                    <label>
                                        <input type="checkbox" name="permisos[]" value="<?php echo $permiso; ?>" 
                                            <?php echo in_array($permiso, $permisosBasicos) ? 'checked disabled' : ''; ?>>
                                        <?php echo htmlspecialchars($permiso); ?>
                                        <?php if (in_array($permiso, $permisosBasicos)): ?>
                                            <small>(Permiso básico, no se puede eliminar)</small>
                                        <?php endif; ?>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                            
                            <div class="acciones">
                                <button type="submit" name="permisos_personalizados" class="btn btn-primary">Guardar Permisos Personalizados</button>
                            </div>
                        </form>
                    <?php else: ?>
                        <p>No tiene permisos asignados.</p>
                    <?php endif; ?>
                </div>
                
                <div class="permisos-a-eliminar">
                    <h3>Permisos a Eliminar</h3>
                    <?php if (!empty($permisosAEliminar)): ?>
                        <ul>
                            <?php foreach ($permisosAEliminar as $permiso): ?>
                                <li class="permiso-item permiso-eliminar"><?php echo htmlspecialchars($permiso); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        
                        <form method="post">
                            <button type="submit" name="eliminar_permisos" class="btn btn-danger">Eliminar Todos los Permisos Excepto los Básicos</button>
                        </form>
                    <?php else: ?>
                        <p>No hay permisos adicionales para eliminar.</p>
                    <?php endif; ?>
                </div>
            </div>
            
            <div style="margin-top: 20px;">
                <a href="admin_permisos_usuario.php?user_id=<?php echo $userId; ?>" class="btn">Volver a Administrar Permisos</a>
                <a href="limpiar_sesion.php" class="btn btn-warning">Limpiar Caché y Reiniciar Sesión</a>
            </div>
        <?php endif; ?>
    </main>
</body>
</html>
