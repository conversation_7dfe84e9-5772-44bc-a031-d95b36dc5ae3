<?php
// Incluir archivo de autenticación
require_once 'auth.php';

// Establecer el modo de visualización de errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Prueba de Autenticación</h1>";

// Procesar formulario
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    echo "<h2>Resultados de la prueba</h2>";
    
    if (empty($username) || empty($password)) {
        echo "<p style='color: red; font-weight: bold;'>Por favor, ingrese usuario y contraseña</p>";
    } else {
        try {
            // Obtener conexión a la base de datos
            $pdo = getConexion();
            
            // Verificar si el usuario existe
            $sql = "SELECT id, username, password, nombre, email, rol FROM usuarios WHERE username = :username";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':username', $username);
            $stmt->execute();
            
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                echo "<p style='color: green; font-weight: bold;'>Usuario encontrado en la base de datos!</p>";
                
                // Mostrar información del usuario (excepto la contraseña)
                echo "<h3>Información del usuario</h3>";
                echo "<ul>";
                foreach ($user as $key => $value) {
                    if ($key !== 'PASSWORD' && $key !== 'password') {
                        echo "<li><strong>" . htmlspecialchars($key) . ":</strong> " . htmlspecialchars($value) . "</li>";
                    }
                }
                echo "</ul>";
                
                // Verificar la contraseña
                $passwordHash = isset($user['PASSWORD']) ? $user['PASSWORD'] : $user['password'];
                
                echo "<h3>Verificación de contraseña</h3>";
                echo "<p>Contraseña ingresada: " . htmlspecialchars($password) . "</p>";
                echo "<p>Hash almacenado: " . htmlspecialchars($passwordHash) . "</p>";
                
                if (password_verify($password, $passwordHash)) {
                    echo "<p style='color: green; font-weight: bold;'>¡La contraseña es correcta!</p>";
                    
                    // Intentar iniciar sesión
                    if (login($username, $password)) {
                        echo "<p style='color: green; font-weight: bold;'>¡Inicio de sesión exitoso!</p>";
                        echo "<p>Sesión iniciada como: " . $_SESSION['user_username'] . " (Rol: " . $_SESSION['user_rol'] . ")</p>";
                        echo "<p><a href='index.php'>Ir a la página principal</a></p>";
                    } else {
                        echo "<p style='color: red; font-weight: bold;'>Error al iniciar sesión a través de la función login()</p>";
                    }
                } else {
                    echo "<p style='color: red; font-weight: bold;'>La contraseña es incorrecta</p>";
                    
                    // Verificar si la contraseña está almacenada sin hash
                    if ($password === $passwordHash) {
                        echo "<p style='color: orange; font-weight: bold;'>Advertencia: La contraseña está almacenada sin hash</p>";
                        echo "<p>Deberías actualizar la contraseña para usar un hash seguro.</p>";
                    }
                }
            } else {
                echo "<p style='color: red; font-weight: bold;'>Usuario no encontrado en la base de datos</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red; font-weight: bold;'>Error de conexión a la base de datos!</p>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
        }
    }
}

// Formulario de prueba
echo "<h2>Formulario de prueba</h2>";
echo "<form method='post'>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='username'>Usuario:</label>";
echo "<input type='text' id='username' name='username' value='" . (isset($_POST['username']) ? htmlspecialchars($_POST['username']) : '') . "'>";
echo "</div>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='password'>Contraseña:</label>";
echo "<input type='password' id='password' name='password' value='" . (isset($_POST['password']) ? htmlspecialchars($_POST['password']) : '') . "'>";
echo "</div>";
echo "<button type='submit'>Probar Autenticación</button>";
echo "</form>";

// Información de depuración
echo "<h2>Información de depuración</h2>";

// Verificar si la función password_hash está disponible
echo "<h3>Funciones de hash de contraseñas</h3>";
echo "<ul>";
echo "<li>password_hash: " . (function_exists('password_hash') ? '<span style="color:green">Disponible</span>' : '<span style="color:red">No disponible</span>') . "</li>";
echo "<li>password_verify: " . (function_exists('password_verify') ? '<span style="color:green">Disponible</span>' : '<span style="color:red">No disponible</span>') . "</li>";
echo "</ul>";

// Mostrar información de la sesión
echo "<h3>Información de la sesión</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Mostrar información de PHP
echo "<h3>Información de PHP</h3>";
echo "<ul>";
echo "<li>Versión de PHP: " . phpversion() . "</li>";
echo "<li>Extensiones cargadas: " . implode(', ', get_loaded_extensions()) . "</li>";
echo "</ul>";

// Formulario para crear un usuario de prueba
echo "<h2>Crear Usuario de Prueba</h2>";
echo "<form method='post' action='crear_usuario_prueba.php'>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='username_test'>Usuario:</label>";
echo "<input type='text' id='username_test' name='username' value='test'>";
echo "</div>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='password_test'>Contraseña:</label>";
echo "<input type='password' id='password_test' name='password' value='test123'>";
echo "</div>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='nombre_test'>Nombre:</label>";
echo "<input type='text' id='nombre_test' name='nombre' value='Usuario de Prueba'>";
echo "</div>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='email_test'>Email:</label>";
echo "<input type='email' id='email_test' name='email' value='<EMAIL>'>";
echo "</div>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='rol_test'>Rol:</label>";
echo "<select id='rol_test' name='rol'>";
echo "<option value='usuario'>Usuario</option>";
echo "<option value='admin'>Administrador</option>";
echo "</select>";
echo "</div>";
echo "<button type='submit'>Crear Usuario de Prueba</button>";
echo "</form>";
?>
