<?php
// Incluir archivo de conexión y permisos
require_once 'conexion.php';
require_once 'permisos.php';
require_once 'auth.php';

// Verificar si el usuario está autenticado y es administrador
requireAdmin();

// Inicializar variables
$mensaje = '';
$error = '';
$usuario = null;
$permisos = [];

// Función para corregir automáticamente los permisos de todos los usuarios
function corregirPermisosAutomaticamente() {
    try {
        $pdo = getConexion();
        $mensajes = [];

        // Verificar si la tabla existe
        try {
            $sql = "SELECT 1 FROM permisos_usuario WHERE ROWNUM = 1";
            $pdo->query($sql);
            $mensajes[] = "La tabla permisos_usuario existe.";
        } catch (PDOException $e) {
            // La tabla no existe, crearla
            $mensajes[] = "La tabla permisos_usuario no existe. Creando tabla...";

            try {
                $sql = "CREATE TABLE permisos_usuario (
                    id_usuario NUMBER(10) NOT NULL,
                    permisos VARCHAR2(4000),
                    CONSTRAINT pk_permisos_usuario PRIMARY KEY (id_usuario),
                    CONSTRAINT fk_permisos_usuario FOREIGN KEY (id_usuario) REFERENCES usuarios(id)
                )";
                $pdo->exec($sql);
                $mensajes[] = "Tabla permisos_usuario creada correctamente.";
            } catch (PDOException $e2) {
                // Intentar con el script
                try {
                    include 'crear_tabla_permisos.php';
                    $mensajes[] = "Tabla permisos_usuario creada usando el script crear_tabla_permisos.php.";
                } catch (Exception $e3) {
                    throw new Exception("Error al crear la tabla de permisos: " . $e3->getMessage());
                }
            }
        }

        // Obtener todos los usuarios
        $sql = "SELECT id, username, rol FROM usuarios";
        $stmt = $pdo->query($sql);
        $usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $usuariosActualizados = 0;

        foreach ($usuarios as $usuario) {
            $userId = isset($usuario['ID']) ? $usuario['ID'] : $usuario['id'];
            $username = isset($usuario['USERNAME']) ? $usuario['USERNAME'] : $usuario['username'];
            $rol = isset($usuario['ROL']) ? $usuario['ROL'] : $usuario['rol'];

            // Verificar si ya tiene permisos
            $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':id_usuario', $userId);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                // Ya tiene permisos, verificar si tiene los permisos básicos
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $permisos = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];
                $permisosArray = explode(',', $permisos);
                $permisosArray = array_map('trim', $permisosArray);

                // Asegurarse de que tenga el permiso de exportar_excel
                $permisosModificados = false;

                if (!in_array('exportar_excel', $permisosArray)) {
                    $permisosArray[] = 'exportar_excel';
                    $permisosModificados = true;
                }

                // Si es rol usuario, asegurarse de que tenga los permisos básicos
                if ($rol === 'usuario') {
                    $permisosBasicos = ['exportar_excel'];

                    foreach ($permisosBasicos as $permiso) {
                        if (!in_array($permiso, $permisosArray)) {
                            $permisosArray[] = $permiso;
                            $permisosModificados = true;
                        }
                    }
                }

                // Si se modificaron los permisos, actualizarlos
                if ($permisosModificados) {
                    $permisosStr = implode(',', $permisosArray);
                    $sql = "UPDATE permisos_usuario SET permisos = :permisos WHERE id_usuario = :id_usuario";
                    $stmt = $pdo->prepare($sql);
                    $stmt->bindParam(':id_usuario', $userId);
                    $stmt->bindParam(':permisos', $permisosStr);
                    $stmt->execute();

                    $mensajes[] = "Permisos actualizados para el usuario $username (ID: $userId).";
                    $usuariosActualizados++;
                }
            } else {
                // No tiene permisos, asignarle los permisos por defecto según su rol
                $permisosRol = getPermisosRol($rol);

                // Asegurarse de que tenga el permiso de exportar_excel
                if (!in_array('exportar_excel', $permisosRol)) {
                    $permisosRol[] = 'exportar_excel';
                }

                $permisosStr = implode(',', $permisosRol);
                $sql = "INSERT INTO permisos_usuario (id_usuario, permisos) VALUES (:id_usuario, :permisos)";
                $stmt = $pdo->prepare($sql);
                $stmt->bindParam(':id_usuario', $userId);
                $stmt->bindParam(':permisos', $permisosStr);
                $stmt->execute();

                $mensajes[] = "Permisos asignados al usuario $username (ID: $userId).";
                $usuariosActualizados++;
            }
        }

        $mensajes[] = "Total de usuarios actualizados: $usuariosActualizados de " . count($usuarios) . ".";

        // Limpiar la caché de permisos
        if (isset($_SESSION['permisos_cache'])) {
            unset($_SESSION['permisos_cache']);
            $mensajes[] = "Caché de permisos limpiada.";
        }

        return ['success' => true, 'mensajes' => $mensajes];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Procesar el formulario
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['corregir_automaticamente'])) {
        $resultado = corregirPermisosAutomaticamente();

        if ($resultado['success']) {
            $mensaje = implode("<br>", $resultado['mensajes']);
        } else {
            $error = $resultado['error'];
        }
    } elseif (isset($_POST['buscar_usuario'])) {
        $username = $_POST['username'];

        try {
            $pdo = getConexion();

            // Buscar el usuario por nombre de usuario
            $sql = "SELECT id, username, nombre, rol FROM usuarios WHERE username = :username";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':username', $username);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $usuario = $stmt->fetch(PDO::FETCH_ASSOC);
                $userId = isset($usuario['ID']) ? $usuario['ID'] : $usuario['id'];

                // Obtener los permisos del usuario
                $permisos = getPermisosUsuario($userId, true);
            } else {
                $error = "No se encontró el usuario '$username'";
            }
        } catch (PDOException $e) {
            $error = "Error en la base de datos: " . $e->getMessage();
        }
    } elseif (isset($_POST['guardar_permisos'])) {
        $userId = $_POST['user_id'];
        $permisosSeleccionados = isset($_POST['permisos']) ? $_POST['permisos'] : [];

        $result = guardarPermisosUsuario($userId, $permisosSeleccionados);
        if ($result === true) {
            $mensaje = "Permisos guardados correctamente";

            // Obtener información del usuario
            try {
                $pdo = getConexion();
                $sql = "SELECT id, username, nombre, rol FROM usuarios WHERE id = :id";
                $stmt = $pdo->prepare($sql);
                $stmt->bindParam(':id', $userId);
                $stmt->execute();

                if ($stmt->rowCount() > 0) {
                    $usuario = $stmt->fetch(PDO::FETCH_ASSOC);

                    // Obtener los permisos actualizados
                    $permisos = getPermisosUsuario($userId, true);
                }
            } catch (PDOException $e) {
                $error = "Error al obtener información del usuario: " . $e->getMessage();
            }
        } else {
            $error = is_string($result) ? $result : "Error al guardar los permisos";
        }
    } elseif (isset($_POST['limpiar_cache'])) {
        // Limpiar la caché de permisos
        if (isset($_SESSION['permisos_cache'])) {
            unset($_SESSION['permisos_cache']);
            $mensaje = "Caché de permisos limpiada correctamente";
        }
    }
}

// Obtener todos los permisos disponibles
$todosPermisos = getPermisos();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Corregir Permisos de Usuario - Sistema de Reportes de Sueldos</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .form-container {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .permisos-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .permiso-item {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            width: calc(33.33% - 10px);
        }

        .mensaje {
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 4px;
        }

        .mensaje.exito {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .mensaje.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>Sistema de Reportes de Sueldos</h1>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul>
                <li><a href="index.php">Inicio</a></li>
                <li><a href="reportes.php">Reportes</a></li>
                <li><a href="admin_usuarios.php">Administrar Usuarios</a></li>
                <li><a href="admin_permisos_usuario.php">Administrar Permisos</a></li>
                <li><a href="logout.php">Cerrar Sesión (<?php echo htmlspecialchars($_SESSION['user_username']); ?>)</a></li>
            </ul>
        </div>
    </nav>

    <main class="container">
        <h2>Corregir Permisos de Usuario</h2>

        <?php if (!empty($mensaje)): ?>
            <div class="mensaje exito"><?php echo $mensaje; ?></div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="mensaje error"><?php echo $error; ?></div>
        <?php endif; ?>

        <div class="form-container">
            <h3>Buscar Usuario</h3>
            <form method="post">
                <div class="form-group">
                    <label for="username">Nombre de Usuario:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <button type="submit" name="buscar_usuario" class="btn">Buscar</button>
            </form>
        </div>

        <?php if ($usuario): ?>
            <div class="form-container">
                <h3>Permisos de Usuario: <?php echo htmlspecialchars(isset($usuario['USERNAME']) ? $usuario['USERNAME'] : $usuario['username']); ?></h3>
                <p><strong>Nombre:</strong> <?php echo htmlspecialchars(isset($usuario['NOMBRE']) ? $usuario['NOMBRE'] : $usuario['nombre']); ?></p>
                <p><strong>Rol:</strong> <?php echo htmlspecialchars(isset($usuario['ROL']) ? $usuario['ROL'] : $usuario['rol']); ?></p>

                <form method="post">
                    <input type="hidden" name="user_id" value="<?php echo isset($usuario['ID']) ? $usuario['ID'] : $usuario['id']; ?>">

                    <h4>Seleccionar Permisos:</h4>
                    <div class="permisos-container">
                        <?php foreach ($todosPermisos as $codigo => $descripcion): ?>
                            <div class="permiso-item">
                                <label>
                                    <input type="checkbox" name="permisos[]" value="<?php echo $codigo; ?>" <?php echo in_array($codigo, $permisos) ? 'checked' : ''; ?>>
                                    <?php echo htmlspecialchars($descripcion); ?>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div style="margin-top: 20px;">
                        <button type="submit" name="guardar_permisos" class="btn">Guardar Permisos</button>
                    </div>
                </form>
            </div>
        <?php endif; ?>

        <div class="form-container">
            <h3>Herramientas de Corrección</h3>
            <p>Utilice estas herramientas para corregir problemas con los permisos de usuarios:</p>

            <div style="display: flex; gap: 10px; margin-top: 15px;">
                <form method="post">
                    <button type="submit" name="corregir_automaticamente" class="btn btn-success">Corregir permisos automáticamente</button>
                </form>

                <form method="post">
                    <button type="submit" name="limpiar_cache" class="btn btn-warning">Limpiar caché de permisos</button>
                </form>
            </div>

            <div style="margin-top: 15px;">
                <p><strong>Corregir permisos automáticamente:</strong> Verifica y corrige los permisos de todos los usuarios en el sistema.</p>
                <p><strong>Limpiar caché de permisos:</strong> Limpia la caché de permisos para forzar la recarga desde la base de datos.</p>
            </div>
        </div>

        <div style="margin-top: 20px;">
            <p>
                <a href="admin_permisos_usuario.php" class="btn">Volver a Administración de Permisos</a>
                <a href="diagnostico_permisos.php" class="btn">Ver Diagnóstico de Permisos</a>
            </p>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Sistema de Reportes de Sueldos. Todos los derechos reservados.</p>
        </div>
    </footer>
</body>
</html>
