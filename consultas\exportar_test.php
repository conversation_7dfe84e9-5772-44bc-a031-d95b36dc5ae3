<?php
/**
 * Consulta de prueba para exportación a Excel
 *
 * Este archivo contiene una consulta simple para probar la exportación a Excel.
 */

// Verificar si el usuario está autenticado y tiene permiso
if (!isLoggedIn() || (!isAdmin() && !tienePermiso('exportar_excel'))) {
    echo "<p class='error'>No tienes permiso para acceder a este reporte.</p>";
    return;
}

try {
    $pdo = getConexion();

    // Crear tabla temporal para almacenar los resultados
    $pdo->exec("
        BEGIN
            EXECUTE IMMEDIATE 'DROP TABLE TEMP_EXPORTAR_TEST';
        EXCEPTION
            WHEN OTHERS THEN
                IF SQLCODE != -942 THEN
                    RAISE;
                END IF;
        END;
    ");

    $pdo->exec("
        CREATE GLOBAL TEMPORARY TABLE TEMP_EXPORTAR_TEST (
            ID NUMBER(10),
            NOMBRE VARCHAR2(100),
            FECHA DATE,
            IMPORTE NUMBER(10,2)
        ) ON COMMIT PRESERVE ROWS
    ");

    // Insertar datos de ejemplo en la tabla temporal
    for ($i = 1; $i <= 20; $i++) {
        $nombre = "Ejemplo " . $i;
        $fecha = date('Y-m-d', strtotime("-$i days"));
        $importe = 1000 + ($i * 100);

        $sql = "INSERT INTO TEMP_EXPORTAR_TEST (ID, NOMBRE, FECHA, IMPORTE) VALUES (:id, :nombre, TO_DATE(:fecha, 'YYYY-MM-DD'), :importe)";
        $stmt_insert = $pdo->prepare($sql);
        $stmt_insert->bindParam(':id', $i);
        $stmt_insert->bindParam(':nombre', $nombre);
        $stmt_insert->bindParam(':fecha', $fecha);
        $stmt_insert->bindParam(':importe', $importe);
        $stmt_insert->execute();
    }

    // Ejecutar la consulta final
    $sql = "SELECT ID, NOMBRE, TO_CHAR(FECHA, 'DD/MM/YYYY') AS FECHA, IMPORTE FROM TEMP_EXPORTAR_TEST ORDER BY ID";
    $stmt = $pdo->query($sql);

    // Si estamos en modo de exportación a Excel, no mostrar nada en pantalla
    if (basename($_SERVER['PHP_SELF']) === 'exportar_excel.php') {
        // No hacer nada, solo devolver $stmt para que exportar_excel.php lo use
        return;
    }

    // Si estamos en modo de visualización normal, mostrar los resultados en pantalla
    echo "<h3>Resultados de la consulta de prueba</h3>";
    echo "<p>Esta es una consulta de prueba para verificar la exportación a Excel.</p>";

} catch (PDOException $e) {
    echo "<p class='error'>Error en la consulta: " . $e->getMessage() . "</p>";
}
?>
