<?php
// Incluir archivos necesarios
require_once 'conexion.php';
require_once 'permisos.php';

// Establecer el modo de visualización de errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Corrección de Permisos para dpohidraulica</h1>";

try {
    // Obtener conexión a la base de datos
    $pdo = getConexion();
    
    echo "<p style='color: green; font-weight: bold;'>Conexión a la base de datos exitosa!</p>";
    
    // Verificar si existe el usuario dpohidraulica
    $sql = "SELECT * FROM usuarios WHERE username = 'dpohidraulica'";
    $stmt = $pdo->query($sql);
    $dpohidraulica = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($dpohidraulica) {
        $userId = isset($dpohidraulica['ID']) ? $dpohidraulica['ID'] : $dpohidraulica['id'];
        echo "<p style='color: green; font-weight: bold;'>El usuario dpohidraulica existe con ID: $userId</p>";
        
        // Verificar si tiene permisos asignados
        $sql = "SELECT * FROM permisos_usuario WHERE id_usuario = :id_usuario";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id_usuario', $userId);
        $stmt->execute();
        $permisosUsuario = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($permisosUsuario) {
            $permisos = isset($permisosUsuario['PERMISOS']) ? $permisosUsuario['PERMISOS'] : $permisosUsuario['permisos'];
            echo "<p>Permisos actuales: $permisos</p>";
            
            // Formulario para actualizar permisos
            echo "<h3>Actualizar Permisos</h3>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='actualizar_permisos' value='1'>";
            echo "<input type='hidden' name='user_id' value='$userId'>";
            echo "<button type='submit'>Actualizar Permisos para dpohidraulica</button>";
            echo "</form>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>El usuario dpohidraulica NO tiene permisos asignados!</p>";
            
            // Formulario para asignar permisos
            echo "<h3>Asignar Permisos</h3>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='asignar_permisos' value='1'>";
            echo "<input type='hidden' name='user_id' value='$userId'>";
            echo "<button type='submit'>Asignar Permisos a dpohidraulica</button>";
            echo "</form>";
        }
    } else {
        echo "<p style='color: red; font-weight: bold;'>El usuario dpohidraulica NO existe!</p>";
        
        // Formulario para crear el usuario
        echo "<h3>Crear Usuario dpohidraulica</h3>";
        echo "<form method='post'>";
        echo "<input type='hidden' name='crear_usuario' value='1'>";
        echo "<button type='submit'>Crear Usuario dpohidraulica</button>";
        echo "</form>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red; font-weight: bold;'>Error de conexión a la base de datos!</p>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}

// Procesar formulario para crear el usuario
if (isset($_POST['crear_usuario'])) {
    try {
        $pdo = getConexion();
        
        // Datos del usuario
        $username = 'dpohidraulica';
        $password = password_hash('dpohidraulica123', PASSWORD_DEFAULT);
        $nombre = 'DEPARTAMENTO HIDRAULICA';
        $email = '<EMAIL>';
        $rol = 'usuario';
        
        // Insertar el usuario
        $sql = "INSERT INTO usuarios (username, password, nombre, email, rol) VALUES (:username, :password, :nombre, :email, :rol)";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':password', $password);
        $stmt->bindParam(':nombre', $nombre);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':rol', $rol);
        
        if ($stmt->execute()) {
            $id = $pdo->lastInsertId();
            echo "<p style='color: green; font-weight: bold;'>Usuario dpohidraulica creado correctamente con ID: $id</p>";
            echo "<p>Recarga la página para asignar permisos.</p>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>Error al crear el usuario dpohidraulica!</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red; font-weight: bold;'>Error al crear el usuario dpohidraulica!</p>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
    }
}

// Procesar formulario para asignar permisos
if (isset($_POST['asignar_permisos']) || isset($_POST['actualizar_permisos'])) {
    try {
        $userId = $_POST['user_id'];
        
        // Permisos a asignar
        $permisos = [
            'exportar_excel',
            'ver_reporte_hidraulica',
            'ver_reportes'
        ];
        
        // Guardar permisos
        $resultado = guardarPermisosUsuario($userId, $permisos);
        
        if ($resultado === true) {
            echo "<p style='color: green; font-weight: bold;'>Permisos asignados correctamente al usuario dpohidraulica!</p>";
            
            // Verificar los permisos asignados
            $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':id_usuario', $userId);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $permisosGuardados = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];
                echo "<p>Permisos guardados: $permisosGuardados</p>";
            }
            
            // Limpiar la caché de permisos
            echo "<p>Limpiando caché de permisos...</p>";
            if (isset($_SESSION['permisos_cache'])) {
                unset($_SESSION['permisos_cache']);
                echo "<p>Caché de permisos limpiada.</p>";
            }
        } else {
            echo "<p style='color: red; font-weight: bold;'>Error al asignar permisos: $resultado</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red; font-weight: bold;'>Error al asignar permisos!</p>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
    }
}
?>
