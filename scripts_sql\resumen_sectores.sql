-- <PERSON><PERSON>t para el reporte de Resumen por Sectores

-- Consulta final
SELECT
    a.CENTRO,
    a.SECTOR,
    COUNT(a.PADROND) as CANTIDAD,
    SUM(ASIGAPOR) as TOTAL_ASIGAPOR,
    AVG(ASIGAPOR) as PROMEDIO_ASIGAPOR,
    SUM(APORJUB) as TOTAL_APORJUB,
    AVG(APORJUB) as PROMEDIO_APORJUB
FROM sueldo0425 a
JOIN imponi0425B b ON a.PADROND = b.PADROND
GROUP BY a.CENTRO, a.SECTOR
ORDER BY a.CENTRO, a.SECTOR
