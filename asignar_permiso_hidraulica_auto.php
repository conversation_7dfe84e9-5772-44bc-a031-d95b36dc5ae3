<?php
// Incluir archivo de conexión y permisos
require_once 'conexion.php';
require_once 'permisos.php';

// Verificar que el usuario esté autenticado
requireLogin();

// Obtener el ID del usuario actual
$userId = $_SESSION['user_id'];
$username = $_SESSION['user_username'];

try {
    $pdo = getConexion();

    // Permisos a asignar
    $permisos = [
        'exportar_excel',
        'ver_reporte_hidraulica',
        'ver_reporte_obra_social'
    ];

    // Convertir el array de permisos a una cadena separada por comas
    $permisosStr = implode(',', $permisos);

    // Verificar si ya existen permisos para este usuario
    $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id_usuario', $userId);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        // Obtener permisos existentes
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $permisosExistentes = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];
        
        // Convertir a array
        $permisosArray = explode(',', $permisosExistentes);
        
        // Agregar nuevos permisos sin duplicar
        foreach ($permisos as $permiso) {
            if (!in_array($permiso, $permisosArray)) {
                $permisosArray[] = $permiso;
            }
        }
        
        // Convertir de nuevo a string
        $permisosStr = implode(',', $permisosArray);
        
        // Actualizar permisos existentes
        $sql = "UPDATE permisos_usuario SET permisos = :permisos WHERE id_usuario = :id_usuario";
    } else {
        // Insertar nuevos permisos
        $sql = "INSERT INTO permisos_usuario (id_usuario, permisos) VALUES (:id_usuario, :permisos)";
    }

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id_usuario', $userId);
    $stmt->bindParam(':permisos', $permisosStr);

    if ($stmt->execute()) {
        // Limpiar la caché de permisos
        if (isset($_SESSION['permisos_cache'][$userId])) {
            unset($_SESSION['permisos_cache'][$userId]);
        }
        if (isset($_SESSION['permisos_cache'])) {
            unset($_SESSION['permisos_cache']);
        }
        
        // Redirigir a la página de reportes
        header('Location: reportes.php');
        exit;
    } else {
        $error = $stmt->errorInfo();
        echo "Error al guardar los permisos: " . $error[2];
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
