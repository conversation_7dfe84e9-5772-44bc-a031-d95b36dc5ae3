<?php
// Incluir archivo de permisos
require_once 'permisos.php';

// Verificar que el usuario esté autenticado y tenga permiso
requireLogin();
requirePermiso('admin_usuarios');

$mensaje = '';
$error = '';

// Procesar eliminación de usuario
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $id = $_GET['delete'];

    // No permitir eliminar al propio usuario
    if ($id == $_SESSION['user_id']) {
        $error = 'No puedes eliminar tu propio usuario';
    } else {
        $result = deleteUser($id);

        if ($result === true) {
            $mensaje = 'Usuario eliminado correctamente';
        } else {
            $error = $result;
        }
    }
}

// Obtener todos los usuarios
$usuarios = getAllUsers();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administración de Usuarios - Sistema de Consultas Oracle</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .user-actions {
            display: flex;
            gap: 5px;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
<?php include 'header_with_user.php'; ?>

    <nav>
        <div class="container">
            <ul>
                <li><a href="index.php">Inicio</a></li>
                <?php
                // Verificar si el usuario tiene permiso para ver al menos una consulta
                $mostrarReportes = tienePermiso('ver_reportes');
                if (!$mostrarReportes) {
                    // Verificar permisos específicos para consultas
                    if (tienePermiso('ver_consulta_hidraulica') || tienePermiso('ver_consulta_policia')) {
                        $mostrarReportes = true;
                    }
                }
                if ($mostrarReportes):
                ?>
                    <li><a href="reportes.php">Reportes</a></li>
                <?php endif; ?>
                <?php if (tienePermiso('admin_usuarios')): ?>
                    <li><a href="admin_usuarios.php" class="active">Administrar Usuarios</a></li>
                    <li><a href="admin_permisos_usuario.php">Administrar Permisos</a></li>
                    <li><a href="corregir_permisos.php">Corregir Permisos</a></li>
                <?php endif; ?>
                <li><a href="logout.php">Cerrar Sesión (<?php echo htmlspecialchars($_SESSION['user_username']); ?>)</a></li>
            </ul>
        </div>
    </nav>

    <main class="container">
        <h2>Administración de Usuarios</h2>

        <?php if (!empty($mensaje)): ?>
            <div class="success"><?php echo $mensaje; ?></div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>

        <div style="margin-bottom: 20px;">
            <a href="admin_usuario_form.php" class="btn btn-success">Nuevo Usuario</a>
        </div>

        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Usuario</th>
                    <th>Nombre</th>
                    <th>Email</th>
                    <th>Rol</th>
                    <th>Fecha Creación</th>
                    <th>Estado</th>
                    <th>Acciones</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($usuarios): ?>
                    <?php foreach ($usuarios as $usuario): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($usuario['id']); ?></td>
                            <td><?php echo htmlspecialchars($usuario['username']); ?></td>
                            <td><?php echo htmlspecialchars($usuario['nombre']); ?></td>
                            <td><?php echo htmlspecialchars($usuario['email']); ?></td>
                            <td><?php echo htmlspecialchars($usuario['rol']); ?></td>
                            <td><?php echo htmlspecialchars($usuario['fecha_creacion']); ?></td>
                            <td><?php echo $usuario['activo'] ? 'Activo' : 'Inactivo'; ?></td>
                            <td class="user-actions">
                                <a href="admin_usuario_form.php?id=<?php echo $usuario['id']; ?>" class="btn btn-warning btn-sm">Editar</a>
                                <?php if ($usuario['id'] != $_SESSION['user_id']): ?>
                                    <a href="admin_usuarios.php?delete=<?php echo $usuario['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('¿Estás seguro de que deseas eliminar este usuario?')">Eliminar</a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="8">No hay usuarios registrados</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Sistema de Consultas Oracle. Todos los derechos reservados.</p>
        </div>
    </footer>
</body>
</html>
