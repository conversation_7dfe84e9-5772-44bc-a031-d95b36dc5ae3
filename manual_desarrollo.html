<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Desarrollo - Sistema de Reportes de Sueldos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #2980b9;
            border-left: 4px solid #3498db;
            padding-left: 10px;
            margin-top: 30px;
        }
        h3 {
            color: #3498db;
            margin-top: 25px;
        }
        h4 {
            color: #2c3e50;
            margin-top: 20px;
        }
        pre {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            overflow-x: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 90%;
            color: #e74c3c;
        }
        .note {
            background-color: #f8f9fa;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
        }
        .tip {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .toc {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 20px;
        }
        .toc li {
            margin-bottom: 8px;
        }
        .toc a {
            text-decoration: none;
            color: #3498db;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #777;
        }
        .file-structure {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            line-height: 1.5;
            padding-left: 20px;
        }
        .file-structure ul {
            list-style-type: none;
            padding-left: 20px;
        }
        .file-structure li {
            margin-bottom: 5px;
        }
        .file-structure .folder:before {
            content: "📁 ";
        }
        .file-structure .file:before {
            content: "📄 ";
        }
        .file-structure .php:before {
            content: "🐘 ";
        }
        .file-structure .sql:before {
            content: "🗃️ ";
        }
        .file-structure .css:before {
            content: "🎨 ";
        }
        .file-structure .js:before {
            content: "⚙️ ";
        }
        .file-structure .html:before {
            content: "🌐 ";
        }
        @media print {
            body {
                font-size: 12pt;
            }
            h1 {
                font-size: 18pt;
            }
            h2 {
                font-size: 16pt;
            }
            h3 {
                font-size: 14pt;
            }
            pre, code {
                font-size: 10pt;
            }
            .no-print {
                display: none;
            }
            a {
                text-decoration: none;
                color: #333;
            }
        }
    </style>
</head>
<body>
    <h1>Manual de Desarrollo<br>Sistema de Reportes de Sueldos</h1>

    <div class="no-print" style="background-color: #e9f7fe; padding: 15px; margin-bottom: 20px; border-radius: 5px; border-left: 4px solid #3498db;">
        <h3 style="margin-top: 0; color: #2980b9;">Cómo convertir este manual a PDF</h3>
        <p>Para guardar este manual como PDF, siga estos pasos:</p>
        <ol>
            <li>Presione <strong>Ctrl+P</strong> (Windows/Linux) o <strong>Cmd+P</strong> (Mac) para abrir el diálogo de impresión.</li>
            <li>En el menú desplegable de "Destino" o "Impresora", seleccione <strong>Guardar como PDF</strong>.</li>
            <li>Haga clic en <strong>Guardar</strong> y elija la ubicación donde desea guardar el archivo PDF.</li>
        </ol>
    </div>

    <div class="toc">
        <h2>Índice de Contenidos</h2>
        <ul>
            <li><a href="#introduccion">1. Introducción</a></li>
            <li><a href="#tecnologias">2. Tecnologías Utilizadas</a></li>
            <li><a href="#arquitectura">3. Arquitectura del Sistema</a>
                <ul>
                    <li><a href="#estructura-archivos">3.1. Estructura de Archivos</a></li>
                    <li><a href="#flujo-datos">3.2. Flujo de Datos</a></li>
                    <li><a href="#modulos">3.3. Módulos Principales</a></li>
                </ul>
            </li>
            <li><a href="#base-datos">4. Base de Datos</a>
                <ul>
                    <li><a href="#esquema">4.1. Esquema de la Base de Datos</a></li>
                    <li><a href="#conexion">4.2. Conexión a la Base de Datos</a></li>
                    <li><a href="#consultas">4.3. Consultas SQL</a></li>
                </ul>
            </li>
            <li><a href="#autenticacion">5. Sistema de Autenticación y Autorización</a>
                <ul>
                    <li><a href="#login">5.1. Proceso de Login</a></li>
                    <li><a href="#permisos">5.2. Sistema de Permisos</a></li>
                </ul>
            </li>
            <li><a href="#reportes">6. Sistema de Reportes</a>
                <ul>
                    <li><a href="#configuracion-reportes">6.1. Configuración de Reportes</a></li>
                    <li><a href="#ejecucion-reportes">6.2. Ejecución de Reportes</a></li>
                    <li><a href="#exportacion">6.3. Exportación a Excel</a></li>
                </ul>
            </li>
            <li><a href="#interfaz">7. Interfaz de Usuario</a>
                <ul>
                    <li><a href="#estilos">7.1. Estilos y CSS</a></li>
                    <li><a href="#javascript">7.2. JavaScript</a></li>
                    <li><a href="#responsive">7.3. Diseño Responsive</a></li>
                </ul>
            </li>
            <li><a href="#seguridad">8. Seguridad</a>
                <ul>
                    <li><a href="#proteccion-sql">8.1. Protección contra SQL Injection</a></li>
                    <li><a href="#proteccion-xss">8.2. Protección contra XSS</a></li>
                    <li><a href="#proteccion-csrf">8.3. Protección contra CSRF</a></li>
                </ul>
            </li>
            <li><a href="#despliegue">9. Despliegue</a>
                <ul>
                    <li><a href="#requisitos">9.1. Requisitos del Servidor</a></li>
                    <li><a href="#instalacion">9.2. Proceso de Instalación</a></li>
                    <li><a href="#configuracion-servidor">9.3. Configuración del Servidor</a></li>
                </ul>
            </li>
            <li><a href="#mantenimiento">10. Mantenimiento y Extensión</a>
                <ul>
                    <li><a href="#agregar-reportes">10.1. Cómo Agregar Nuevos Reportes</a></li>
                    <li><a href="#modificar-permisos">10.2. Cómo Modificar Permisos</a></li>
                    <li><a href="#actualizar-bd">10.3. Cómo Actualizar la Base de Datos</a></li>
                </ul>
            </li>
        </ul>
    </div>

    <h2 id="introduccion">1. Introducción</h2>
    <p>El Sistema de Reportes de Sueldos es una aplicación web desarrollada para facilitar el acceso a diferentes reportes relacionados con información de sueldos y datos de personal. Este manual técnico está dirigido a desarrolladores y administradores de sistemas que necesiten entender la arquitectura, el funcionamiento interno y cómo mantener o extender el sistema.</p>

    <p>El sistema fue diseñado con los siguientes objetivos:</p>
    <ul>
        <li>Proporcionar una interfaz web amigable para acceder a reportes de sueldos</li>
        <li>Implementar un sistema de autenticación y autorización basado en roles</li>
        <li>Permitir la exportación de reportes a formato Excel</li>
        <li>Facilitar la administración de usuarios y permisos</li>
        <li>Garantizar la seguridad y protección de datos sensibles</li>
    </ul>

    <h2 id="tecnologias">2. Tecnologías Utilizadas</h2>
    <p>El sistema está desarrollado utilizando las siguientes tecnologías:</p>

    <h3>2.1. Backend</h3>
    <ul>
        <li><strong>PHP:</strong> Versión 7.x o superior para la lógica del servidor</li>
        <li><strong>PDO (PHP Data Objects):</strong> Para la conexión y consultas a la base de datos Oracle</li>
        <li><strong>PHPExcel:</strong> Biblioteca para la generación de archivos Excel</li>
        <li><strong>Dompdf:</strong> Biblioteca para la generación de archivos PDF</li>
    </ul>

    <h3>2.2. Frontend</h3>
    <ul>
        <li><strong>HTML5:</strong> Para la estructura de las páginas</li>
        <li><strong>CSS3:</strong> Para el diseño y estilos</li>
        <li><strong>JavaScript:</strong> Para interactividad en el lado del cliente</li>
        <li><strong>Diseño Responsive:</strong> Para adaptación a diferentes dispositivos</li>
    </ul>

    <h3>2.3. Base de Datos</h3>
    <ul>
        <li><strong>Oracle Database:</strong> Para almacenamiento de datos</li>
        <li><strong>SQL:</strong> Para consultas y manipulación de datos</li>
    </ul>

    <h3>2.4. Herramientas de Desarrollo</h3>
    <ul>
        <li><strong>Composer:</strong> Gestor de dependencias para PHP</li>
        <li><strong>Git:</strong> Sistema de control de versiones</li>
        <li><strong>XAMPP/WAMP:</strong> Entorno de desarrollo local</li>
    </ul>

    <h2 id="arquitectura">3. Arquitectura del Sistema</h2>
    <p>El sistema sigue una arquitectura tradicional de aplicación web PHP, con separación de responsabilidades entre diferentes archivos y módulos.</p>

    <h3 id="estructura-archivos">3.1. Estructura de Archivos</h3>
    <div class="file-structure">
        <ul>
            <li class="php">index.php - Página principal del sistema</li>
            <li class="php">login.php - Página de inicio de sesión</li>
            <li class="php">reportes.php - Página de visualización de reportes</li>
            <li class="php">admin_usuarios.php - Administración de usuarios</li>
            <li class="php">admin_permisos_usuario.php - Administración de permisos</li>
            <li class="php">exportar_excel.php - Exportación a Excel</li>
            <li class="php">exportar_excel_simple.php - Exportación a Excel (versión simple)</li>
            <li class="php">auth.php - Funciones de autenticación</li>
            <li class="php">permisos.php - Funciones de gestión de permisos</li>
            <li class="php">conexion.php - Configuración de conexión a la base de datos</li>
            <li class="php">consultas_sql.php - Definición de consultas disponibles</li>
            <li class="php">header_with_user.php - Encabezado común con información de usuario</li>
            <li class="php">nav_menu.php - Menú de navegación común</li>
            <li class="php">head_common.php - Elementos comunes del head HTML</li>
            <li class="php">logout.php - Cierre de sesión</li>
            <li class="folder">css
                <ul>
                    <li class="css">styles.css - Estilos principales</li>
                </ul>
            </li>
            <li class="folder">scripts_sql
                <ul>
                    <li class="sql">falla_caja.sql - Consulta para reporte de Falla de Caja</li>
                    <li class="sql">san_nicolas.sql - Consulta para reporte de San Nicolás</li>
                    <li class="sql">hidraulica.sql - Consulta para reporte de Hidráulica</li>
                    <li class="sql">universidad_catolica.sql - Consulta para reporte de Universidad Católica</li>
                    <li class="sql">resumen_centros.sql - Consulta para reporte de Resumen por Centros</li>
                    <li class="sql">resumen_sectores.sql - Consulta para reporte de Resumen por Sectores</li>
                </ul>
            </li>
            <li class="folder">images
                <ul>
                    <li class="file">logo.png - Logo del sistema</li>
                </ul>
            </li>
            <li class="folder">vendor - Dependencias de Composer</li>
        </ul>
    </div>

    <h3 id="flujo-datos">3.2. Flujo de Datos</h3>
    <p>El flujo de datos en el sistema sigue el siguiente patrón:</p>
    <ol>
        <li>El usuario accede al sistema a través de login.php</li>
        <li>Tras la autenticación, se redirige a index.php</li>
        <li>El usuario navega a reportes.php para ver los reportes disponibles</li>
        <li>Al seleccionar un reporte, se ejecuta la consulta SQL correspondiente</li>
        <li>Los resultados se muestran en pantalla y pueden ser exportados a Excel</li>
        <li>Los administradores pueden gestionar usuarios y permisos a través de admin_usuarios.php y admin_permisos_usuario.php</li>
    </ol>

    <h3 id="modulos">3.3. Módulos Principales</h3>
    <p>El sistema está organizado en los siguientes módulos principales:</p>

    <h4>3.3.1. Módulo de Autenticación</h4>
    <p>Gestionado principalmente por auth.php, maneja el inicio de sesión, verificación de credenciales y gestión de sesiones.</p>

    <h4>3.3.2. Módulo de Permisos</h4>
    <p>Implementado en permisos.php, gestiona los permisos de los usuarios y controla el acceso a diferentes funcionalidades.</p>

    <h4>3.3.3. Módulo de Reportes</h4>
    <p>Centralizado en reportes.php y consultas_sql.php, maneja la visualización y ejecución de reportes.</p>

    <h4>3.3.4. Módulo de Exportación</h4>
    <p>Implementado en exportar_excel.php y exportar_excel_simple.php, gestiona la exportación de datos a formato Excel.</p>

    <h4>3.3.5. Módulo de Administración</h4>
    <p>Compuesto por admin_usuarios.php y admin_permisos_usuario.php, permite la gestión de usuarios y permisos.</p>

    <h2 id="base-datos">4. Base de Datos</h2>
    <p>El sistema utiliza una base de datos Oracle para almacenar tanto los datos de usuarios y permisos como los datos de sueldos que se consultan en los reportes.</p>

    <h3 id="esquema">4.1. Esquema de la Base de Datos</h3>
    <p>El esquema de la base de datos incluye las siguientes tablas principales:</p>

    <h4>4.1.1. Tablas del Sistema</h4>
    <pre>
-- Tabla de usuarios
CREATE TABLE usuarios (
    id NUMBER PRIMARY KEY,
    username VARCHAR2(50) NOT NULL UNIQUE,
    password VARCHAR2(255) NOT NULL,
    nombre VARCHAR2(100) NOT NULL,
    email VARCHAR2(100) NOT NULL,
    rol VARCHAR2(20) NOT NULL,
    fecha_creacion DATE DEFAULT SYSDATE,
    activo NUMBER(1) DEFAULT 1
) TABLESPACE SUELDOS27;

-- Secuencia para el ID de usuarios
CREATE SEQUENCE usuarios_seq START WITH 1 INCREMENT BY 1;

-- Trigger para autoincrement de usuarios
CREATE OR REPLACE TRIGGER usuarios_trg
BEFORE INSERT ON usuarios
FOR EACH ROW
BEGIN
    SELECT usuarios_seq.NEXTVAL INTO :new.id FROM dual;
END;

-- Tabla de permisos de usuario
CREATE TABLE permisos_usuario (
    id NUMBER PRIMARY KEY,
    id_usuario NUMBER NOT NULL,
    permisos VARCHAR2(1000) NOT NULL,
    fecha_creacion DATE DEFAULT SYSDATE,
    CONSTRAINT fk_permisos_usuario FOREIGN KEY (id_usuario) REFERENCES usuarios(id)
) TABLESPACE SUELDOS27;

-- Secuencia para el ID de permisos
CREATE SEQUENCE permisos_usuario_seq START WITH 1 INCREMENT BY 1;

-- Trigger para autoincrement de permisos
CREATE OR REPLACE TRIGGER permisos_usuario_trg
BEFORE INSERT ON permisos_usuario
FOR EACH ROW
BEGIN
    SELECT permisos_usuario_seq.NEXTVAL INTO :new.id FROM dual;
END;
</pre>

    <h4>4.1.2. Tablas de Datos</h4>
    <p>Las tablas de datos principales utilizadas en los reportes incluyen:</p>
    <ul>
        <li><strong>sueldo0425:</strong> Contiene información principal de sueldos</li>
        <li><strong>imponi0425B:</strong> Contiene información de imponibles</li>
        <li><strong>ciescu0425:</strong> Contiene información de conceptos específicos</li>
        <li><strong>cimens0425:</strong> Contiene información mensual de conceptos</li>
    </ul>

    <h3 id="conexion">4.2. Conexión a la Base de Datos</h3>
    <p>La conexión a la base de datos Oracle se realiza a través de PDO (PHP Data Objects) en el archivo conexion.php:</p>
    <pre>
// Configuración de la conexión a Oracle
$host = '**********';
$port = '1521';
$service = 'DESA920';
$sid = 'DESA920';
$user = 'EXP5';
$pass = 'E*x20_22.';

// Formato de conexión utilizado
$dsn = "oci:dbname={$host}:{$port}/{$sid}";

/**
 * Función para obtener una conexión PDO a la base de datos Oracle
 *
 * @return PDO Objeto PDO con la conexión a la base de datos
 * @throws PDOException Si ocurre un error en la conexión
 */
function getConexion() {
    global $dsn, $user, $pass;

    try {
        $pdo = new PDO($dsn, $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_CASE, PDO::CASE_UPPER);
        $pdo->setAttribute(PDO::ATTR_ORACLE_NULLS, PDO::NULL_TO_STRING);

        return $pdo;
    } catch (PDOException $e) {
        // Intentar con formatos alternativos si el formato principal falla
        // ...

        throw $e;
    }
}
</pre>

    <h3 id="consultas">4.3. Consultas SQL</h3>
    <p>Las consultas SQL para los reportes se almacenan de dos formas:</p>

    <h4>4.3.1. Archivos SQL Separados</h4>
    <p>Las consultas más complejas se almacenan en archivos SQL separados en la carpeta scripts_sql/. Por ejemplo, el archivo falla_caja.sql contiene:</p>
    <pre>
-- Script para el reporte de Falla de Caja
select a.centro,C.NOMCENTRO,a.sector,C.NOMSECTOR,a.padrond,a.cuil,a.apynom,a.categ,
A.ESCALAFON,A.TACAREDU,
NVL(TO_CHAR(b.A05),'0,00') AS A05,
NVL(TO_CHAR(b.A79),'0,00') AS A79
from sueldo0425 a
join
(
SELECT TO_CHAR (a.CENTRO, '00') CENTRO,TO_CHAR (A.sector, '000') SECTOR,TO_CHAR(A.padrond,'0000000') padron,B.*
FROM exp5.sueldo0425 A
join
(
select * from (
select padronD,codigo,importe from EXP5.CIESCU0425
)
PIVOT
(
    sum(importe)
    FOR codigo IN (
'A05' A05,
'A79' A79
))
)
B ON (b.padrond=a.padrond)
-- ... resto de la consulta
</pre>

    <h4>4.3.2. Consultas Directas en PHP</h4>
    <p>Algunas consultas más simples se definen directamente en el archivo consultas_sql.php:</p>
    <pre>
// Definir las consultas disponibles
$consultas = [
    // Categoría: Sin Ajuste
    'falla_caja' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'Falla de Caja',
        'descripcion' => 'Consulta de datos de falla de caja.',
        'archivo' => 'scripts_sql/falla_caja.sql',
        'tipo' => 'sql',
        'permiso' => 'ver_reporte_falla_caja'
    ],
    // ... otras consultas

    // Ejemplo de consulta directa
    'imponible_policia_original' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Imponible Policía (Original)',
        'descripcion' => 'Consulta de datos de imponible para el personal de policía (versión original).',
        'sql' => "SELECT
                    a.CENTRO,
                    a.SECTOR,
                    a.PADROND,
                    a.APYNOM,
                    a.CUIL,
                    a.ANTIG,
                    a.PERM_CAT,
                    a.CATEG,
                    a.ESCALAFON,
                    a.FNACIM,
                    ASIGAPOR,
                    RETROMESANT,
                    OTRORETRO,
                    OTROCONCEPSINAPO,
                    SALARIOSINAPO,
                    APORJUB
                FROM sueldo0425 a
                JOIN imponi0425B b ON a.PADROND = b.PADROND
                WHERE a.CENTRO = 11
                ORDER BY a.CENTRO, a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_consulta_policia'
    ],
];
</pre>

    <h2 id="autenticacion">5. Sistema de Autenticación y Autorización</h2>
    <p>El sistema implementa un mecanismo de autenticación y autorización basado en roles y permisos específicos.</p>

    <h3 id="login">5.1. Proceso de Login</h3>
    <p>El proceso de login está implementado en los archivos login.php y auth.php:</p>

    <h4>5.1.1. Formulario de Login (login.php)</h4>
    <pre>
// Procesar el formulario de login
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';

    if (empty($username) || empty($password)) {
        $error = 'Por favor, ingrese usuario y contraseña';
    } else {
        if (login($username, $password)) {
            header('Location: index.php');
            exit;
        } else {
            $error = 'Usuario o contraseña incorrectos';
        }
    }
}
</pre>

    <h4>5.1.2. Función de Login (auth.php)</h4>
    <pre>
/**
 * Intenta autenticar al usuario con las credenciales proporcionadas
 * @param string $username
 * @param string $password
 * @return bool
 */
function login($username, $password) {
    try {
        $pdo = getConexion();

        $sql = "SELECT id, username, password, nombre, email, rol, activo FROM usuarios WHERE username = :username";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':username', $username);
        $stmt->execute();

        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            $userId = $user['ID'];
            $userUsername = $user['USERNAME'];
            $passwordHash = $user['PASSWORD'];
            $userNombre = $user['NOMBRE'];
            $userEmail = $user['EMAIL'];
            $userRol = $user['ROL'];
            $userActivo = $user['ACTIVO'];

            // Verificar la contraseña
            if (password_verify($password, $passwordHash)) {
                // Guardar datos del usuario en la sesión
                $_SESSION['user_id'] = $userId;
                $_SESSION['user_username'] = $userUsername;
                $_SESSION['user_nombre'] = $userNombre;
                $_SESSION['user_email'] = $userEmail;
                $_SESSION['user_rol'] = $userRol;

                return true;
            }
        }

        return false;
    } catch (PDOException $e) {
        error_log("Error en login: " . $e->getMessage());
        return false;
    }
}
</pre>

    <h3 id="permisos">5.2. Sistema de Permisos</h3>
    <p>El sistema de permisos está implementado en el archivo permisos.php y permite controlar el acceso a diferentes funcionalidades del sistema.</p>

    <h4>5.2.1. Definición de Permisos</h4>
    <pre>
/**
 * Define los permisos disponibles en el sistema
 * @return array
 */
function getPermisos() {
    return [
        // Permisos generales
        'ver_reportes' => 'Ver todos los reportes',
        'exportar_excel' => 'Exportar datos a Excel',
        'admin_usuarios' => 'Administrar usuarios',

        // Permisos para reportes específicos - Sin Ajuste
        'ver_reporte_falla_caja' => 'Ver reporte de Falla de Caja',
        'ver_reporte_san_nicolas' => 'Ver reporte de San Nicolás Mensual',
        'ver_reporte_santisimo_sacramento' => 'Ver reporte de Santísimo Sacramento Mensual',
        'ver_reporte_universidad_catolica' => 'Ver reporte de Universidad Católica Mensual',
        'ver_reporte_poder_judicial' => 'Ver reporte de Imponible Poder Judicial',
        'ver_reporte_reparto_poder_judicial' => 'Ver reporte de Reparto Poder Judicial',

        // Permisos para reportes específicos - Con Ajuste
        'ver_reporte_hidraulica' => 'Ver reporte de Imponible Hidráulica',
        'ver_consulta_policia' => 'Ver reporte de Imponible Policía',
        'ver_reporte_resumen_centros' => 'Ver reporte de Resumen por Centros',
        'ver_reporte_resumen_sectores' => 'Ver reporte de Resumen por Sectores'
    ];
}
</pre>

    <h4>5.2.2. Permisos por Rol</h4>
    <pre>
/**
 * Define los permisos asignados a cada rol
 * @return array
 */
function getPermisosRoles() {
    return [
        'admin' => [
            // Permisos generales
            'ver_reportes',
            'exportar_excel',
            'admin_usuarios',

            // Permisos para reportes específicos
            'ver_reporte_falla_caja',
            'ver_reporte_san_nicolas',
            'ver_reporte_santisimo_sacramento',
            'ver_reporte_universidad_catolica',
            'ver_reporte_poder_judicial',
            'ver_reporte_reparto_poder_judicial',
            'ver_reporte_hidraulica',
            'ver_consulta_policia',
            'ver_reporte_resumen_centros',
            'ver_reporte_resumen_sectores'
        ],
        'usuario' => [
            'exportar_excel'
            // Los usuarios no tienen permisos por defecto, se asignan individualmente
        ]
    ];
}
</pre>

    <h4>5.2.3. Verificación de Permisos</h4>
    <pre>
/**
 * Verifica si el usuario tiene un permiso específico
 * @param string $permiso
 * @param bool $checkDB Si es true, verifica también en la base de datos
 * @return bool
 */
function tienePermiso($permiso, $checkDB = false) {
    // Si el usuario no está autenticado, no tiene permisos
    if (!isLoggedIn()) {
        return false;
    }

    // Si el usuario es administrador, tiene todos los permisos
    if ($_SESSION['user_rol'] === 'admin') {
        return true;
    }

    // Obtener permisos del rol
    $permisosRoles = getPermisosRoles();
    $rol = $_SESSION['user_rol'];

    if (isset($permisosRoles[$rol]) && in_array($permiso, $permisosRoles[$rol])) {
        return true;
    }

    // Verificar en la base de datos si se solicita
    if ($checkDB) {
        try {
            $pdo = getConexion();
            $userId = $_SESSION['user_id'];

            $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':id_usuario', $userId);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $permisos = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];

                if (!empty($permisos)) {
                    $permisosArray = explode(',', $permisos);
                    $permisosArray = array_map('trim', $permisosArray);

                    if (in_array($permiso, $permisosArray)) {
                        return true;
                    }
                }
            }
        } catch (PDOException $e) {
            error_log("Error al verificar permisos en BD: " . $e->getMessage());
        }
    }

    return false;
}
</pre>

    <h2 id="reportes">6. Sistema de Reportes</h2>
    <p>El sistema de reportes es la funcionalidad principal de la aplicación y permite a los usuarios visualizar diferentes reportes según sus permisos.</p>

    <h3 id="configuracion-reportes">6.1. Configuración de Reportes</h3>
    <p>Los reportes disponibles se configuran en el archivo consultas_sql.php:</p>
    <pre>
// Definir las consultas disponibles
$consultas = [
    // Reportes sin ajuste
    'falla_caja' => [
        'titulo' => 'Falla de Caja',
        'descripcion' => 'Reporte de falla de caja para empleados',
        'permiso' => 'ver_reporte_falla_caja',
        'categoria' => 'Sin Ajuste',
        'archivo' => 'scripts_sql/falla_caja.sql',
        'tipo' => 'sql'
    ],
    'san_nicolas' => [
        'titulo' => 'San Nicolás Mensual',
        'descripcion' => 'Reporte mensual de San Nicolás',
        'permiso' => 'ver_reporte_san_nicolas',
        'categoria' => 'Sin Ajuste',
        'archivo' => 'scripts_sql/san_nicolas.sql',
        'tipo' => 'sql'
    ],
    // ... otros reportes
];
</pre>

    <p>Cada reporte tiene los siguientes atributos:</p>
    <ul>
        <li><strong>titulo:</strong> Título del reporte que se muestra en la interfaz</li>
        <li><strong>descripcion:</strong> Descripción breve del reporte</li>
        <li><strong>permiso:</strong> Permiso necesario para acceder al reporte</li>
        <li><strong>categoria:</strong> Categoría del reporte (Sin Ajuste o Con Ajuste)</li>
        <li><strong>archivo:</strong> Ruta al archivo SQL o PHP que contiene la consulta</li>
        <li><strong>tipo:</strong> Tipo de consulta (sql, sql_directo o php)</li>
    </ul>

    <h3 id="ejecucion-reportes">6.2. Ejecución de Reportes</h3>
    <p>La ejecución de reportes se realiza en el archivo reportes.php:</p>
    <pre>
// Obtener información de la consulta
$archivoConsulta = $consultas[$consulta]['archivo'];
$tipoConsulta = isset($consultas[$consulta]['tipo']) ? $consultas[$consulta]['tipo'] : 'php';

if (!file_exists($archivoConsulta)) {
    echo "<p class='error'>El archivo de consulta no existe: " . htmlspecialchars($archivoConsulta) . "</p>";
} else {
    // Ejecutar la consulta según su tipo
    if ($tipoConsulta === 'sql') {
        // Incluir el archivo para ejecutar scripts SQL
        require_once 'ejecutar_script_sql.php';

        // Ejecutar el script SQL
        $stmt = ejecutarScriptSQL($archivoConsulta);

        echo "<h3>" . htmlspecialchars($consultas[$consulta]['titulo']) . "</h3>";
        echo "<p>" . htmlspecialchars($consultas[$consulta]['descripcion']) . "</p>";
    } else if ($tipoConsulta === 'sql_directo') {
        // Ejecutar la consulta SQL directamente
        $sql = $consultas[$consulta]['sql'];

        // Mostrar título y descripción
        echo "<h3>" . htmlspecialchars($consultas[$consulta]['titulo']) . "</h3>";
        echo "<p>" . htmlspecialchars($consultas[$consulta]['descripcion']) . "</p>";

        // Ejecutar la consulta
        try {
            $stmt = $pdo->query($sql);
        } catch (PDOException $e) {
            echo "<p class='error'>Error al ejecutar la consulta SQL: " . $e->getMessage() . "</p>";
            $stmt = null;
        }
    } else {
        // Incluir el archivo PHP directamente
        include $archivoConsulta;
    }
}
</pre>

    <h3 id="exportacion">6.3. Exportación a Excel</h3>
    <p>La exportación a Excel se realiza a través de dos métodos:</p>

    <h4>6.3.1. Exportación Completa (exportar_excel.php)</h4>
    <p>Utiliza la biblioteca PHPExcel para generar archivos Excel con formato:</p>
    <pre>
// Crear un nuevo objeto PHPExcel
$objPHPExcel = new PHPExcel();

// Establecer propiedades del documento
$objPHPExcel->getProperties()->setCreator("Sistema de Reportes de Sueldos")
                             ->setLastModifiedBy("Sistema de Reportes de Sueldos")
                             ->setTitle($consultas[$consulta]['titulo'])
                             ->setSubject("Reporte de datos")
                             ->setDescription($consultas[$consulta]['descripcion'])
                             ->setKeywords("excel php oracle reporte")
                             ->setCategory("Reporte");

// Seleccionar la hoja activa
$objPHPExcel->setActiveSheetIndex(0);
$sheet = $objPHPExcel->getActiveSheet();
$sheet->setTitle(substr($consultas[$consulta]['titulo'], 0, 31));

// Escribir encabezados
$col = 0;
foreach ($columnNames as $header) {
    $sheet->setCellValueByColumnAndRow($col, 1, $header);
    $col++;
}

// Aplicar estilo a los encabezados
$lastCol = PHPExcel_Cell::stringFromColumnIndex($col - 1);
$sheet->getStyle("A1:{$lastCol}1")->getFont()->setBold(true);
$sheet->getStyle("A1:{$lastCol}1")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('DDDDDD');

// Escribir los datos
$row = 2;
while ($fila = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $col = 0;
    foreach ($columnNames as $key) {
        $valor = isset($fila[$key]) ? $fila[$key] : '';
        $sheet->setCellValueByColumnAndRow($col, $row, $valor);
        $col++;
    }
    $row++;
}

// Autoajustar columnas
foreach (range('A', $lastCol) as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// Crear el escritor de Excel
$writer = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');

// Guardar el archivo en el búfer
$writer->save('php://output');
</pre>

    <h4>6.3.2. Exportación Simple (exportar_excel_simple.php)</h4>
    <p>Utiliza funciones nativas de PHP para generar archivos CSV con formato Excel:</p>
    <pre>
// Configurar encabezados para descarga como Excel
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: max-age=0');

// Crear un manejador de archivo para escribir en php://output
$output = fopen('php://output', 'w');

// Escribir el BOM (Byte Order Mark) para UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Escribir los encabezados
fputcsv($output, $columnNames, ';');

// Escribir los datos
foreach ($data as $row) {
    // Convertir valores numéricos a formato español (coma como separador decimal)
    foreach ($row as $key => $value) {
        if (is_numeric($value)) {
            $row[$key] = str_replace('.', ',', $value);
        }
    }
    fputcsv($output, $row, ';');
}

// Cerrar el manejador de archivo
fclose($output);
</pre>

    <h2 id="interfaz">7. Interfaz de Usuario</h2>
    <p>La interfaz de usuario del sistema está diseñada para ser intuitiva, responsive y fácil de usar.</p>

    <h3 id="estilos">7.1. Estilos y CSS</h3>
    <p>Los estilos principales del sistema se definen en el archivo css/styles.css:</p>
    <pre>
/* Estilos generales */
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 15px;
}

/* Encabezado */
header {
    background-color: #2c3e50;
    color: white;
    padding: 20px 0;
    text-align: center;
}

header h1 {
    margin: 0;
    font-size: 28px;
}

/* Navegación */
nav {
    background-color: #34495e;
    color: white;
    padding: 10px 0;
}

nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
}

nav li {
    margin-right: 20px;
}

nav a {
    color: white;
    text-decoration: none;
    font-weight: bold;
}

nav a:hover {
    color: #3498db;
}

/* ... más estilos ... */
</pre>

    <h3 id="javascript">7.2. JavaScript</h3>
    <p>El sistema utiliza JavaScript para mejorar la interactividad en el lado del cliente. Por ejemplo, el código para manejar las pestañas en la página de reportes:</p>
    <pre>
// Código para manejar las pestañas
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remover la clase active de todos los botones y paneles
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Agregar la clase active al botón clickeado
            this.classList.add('active');

            // Mostrar el panel correspondiente
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });
});
</pre>

    <h3 id="responsive">7.3. Diseño Responsive</h3>
    <p>El sistema utiliza técnicas de diseño responsive para adaptarse a diferentes tamaños de pantalla:</p>
    <pre>
/* Media queries para diseño responsive */
@media (max-width: 768px) {
    .card-container {
        flex-direction: column;
    }

    .card {
        width: 100%;
        margin-bottom: 20px;
    }

    .reportes-container {
        flex-direction: column;
    }

    .reportes-menu, .reporte-content {
        width: 100%;
        min-width: auto;
    }

    nav ul {
        flex-direction: column;
    }

    nav li {
        margin-right: 0;
        margin-bottom: 10px;
    }
}
</pre>

    <h2 id="seguridad">8. Seguridad</h2>
    <p>El sistema implementa varias medidas de seguridad para proteger los datos y prevenir ataques comunes.</p>

    <h3 id="proteccion-sql">8.1. Protección contra SQL Injection</h3>
    <p>Se utilizan consultas preparadas con PDO para prevenir ataques de SQL Injection:</p>
    <pre>
$sql = "SELECT id, username, password, nombre, email, rol, activo FROM usuarios WHERE username = :username";
$stmt = $pdo->prepare($sql);
$stmt->bindParam(':username', $username);
$stmt->execute();
</pre>

    <h3 id="proteccion-xss">8.2. Protección contra XSS</h3>
    <p>Se utiliza la función htmlspecialchars() para escapar datos que se muestran en la interfaz:</p>
    <pre>
echo "<h3>" . htmlspecialchars($consultas[$consulta]['titulo']) . "</h3>";
echo "<p>" . htmlspecialchars($consultas[$consulta]['descripcion']) . "</p>";
</pre>

    <h3 id="proteccion-csrf">8.3. Protección contra CSRF</h3>
    <p>Para formularios críticos, se implementa protección contra CSRF mediante tokens:</p>
    <pre>
// Generar token CSRF
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// En el formulario
echo '<input type="hidden" name="csrf_token" value="' . $_SESSION['csrf_token'] . '">';

// Al procesar el formulario
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
    die('Error de validación CSRF');
}
</pre>

    <h2 id="despliegue">9. Despliegue</h2>
    <p>Esta sección describe los requisitos y pasos para desplegar el sistema en un entorno de producción.</p>

    <h3 id="requisitos">9.1. Requisitos del Servidor</h3>
    <ul>
        <li><strong>Servidor Web:</strong> Apache 2.4+ o Nginx</li>
        <li><strong>PHP:</strong> Versión 7.2+ con extensiones:
            <ul>
                <li>PDO y PDO_OCI para conexión a Oracle</li>
                <li>mbstring para manejo de caracteres multibyte</li>
                <li>zip para exportación a Excel</li>
                <li>gd para manipulación de imágenes</li>
            </ul>
        </li>
        <li><strong>Oracle Client:</strong> Oracle Instant Client 12.1+</li>
        <li><strong>Memoria:</strong> Mínimo 2GB de RAM</li>
        <li><strong>Espacio en disco:</strong> Mínimo 500MB para la aplicación</li>
    </ul>

    <h3 id="instalacion">9.2. Proceso de Instalación</h3>
    <ol>
        <li><strong>Clonar el repositorio:</strong>
            <pre>git clone https://github.com/usuario/sistema-reportes-sueldos.git</pre>
        </li>
        <li><strong>Instalar dependencias con Composer:</strong>
            <pre>cd sistema-reportes-sueldos
composer install</pre>
        </li>
        <li><strong>Configurar la conexión a la base de datos:</strong>
            <p>Editar el archivo conexion.php con los datos de conexión correctos.</p>
        </li>
        <li><strong>Crear las tablas del sistema:</strong>
            <p>Ejecutar los scripts crear_tabla_usuarios.php y crear_tabla_permisos.php.</p>
        </li>
        <li><strong>Crear el usuario administrador:</strong>
            <pre>php crear_admin.php</pre>
        </li>
        <li><strong>Configurar permisos de archivos:</strong>
            <pre>chmod -R 755 .
chmod -R 777 logs/</pre>
        </li>
    </ol>

    <h3 id="configuracion-servidor">9.3. Configuración del Servidor</h3>
    <p>Ejemplo de configuración para Apache:</p>
    <pre>
&lt;VirtualHost *:80&gt;
    ServerName reportes.ejemplo.com
    DocumentRoot /var/www/html/sistema-reportes-sueldos

    &lt;Directory /var/www/html/sistema-reportes-sueldos&gt;
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
    &lt;/Directory&gt;

    ErrorLog ${APACHE_LOG_DIR}/reportes-error.log
    CustomLog ${APACHE_LOG_DIR}/reportes-access.log combined
&lt;/VirtualHost&gt;
</pre>

    <h2 id="mantenimiento">10. Mantenimiento y Extensión</h2>
    <p>Esta sección proporciona guías para el mantenimiento y extensión del sistema.</p>

    <h3 id="agregar-reportes">10.1. Cómo Agregar Nuevos Reportes</h3>
    <p>Para agregar un nuevo reporte al sistema:</p>
    <ol>
        <li><strong>Crear el archivo SQL:</strong>
            <p>Crear un nuevo archivo .sql en la carpeta scripts_sql/ con la consulta SQL del reporte.</p>
        </li>
        <li><strong>Registrar el reporte:</strong>
            <p>Agregar la definición del reporte en el archivo consultas_sql.php:</p>
            <pre>
'nuevo_reporte' => [
    'titulo' => 'Título del Nuevo Reporte',
    'descripcion' => 'Descripción del nuevo reporte',
    'permiso' => 'ver_reporte_nuevo',
    'categoria' => 'Sin Ajuste', // o 'Con Ajuste'
    'archivo' => 'scripts_sql/nuevo_reporte.sql',
    'tipo' => 'sql'
],</pre>
        </li>
        <li><strong>Agregar el permiso:</strong>
            <p>Agregar el nuevo permiso en la función getPermisos() en permisos.php:</p>
            <pre>'ver_reporte_nuevo' => 'Ver reporte nuevo',</pre>
        </li>
        <li><strong>Asignar el permiso a roles:</strong>
            <p>Agregar el permiso al rol de administrador en la función getPermisosRoles() en permisos.php.</p>
        </li>
    </ol>

    <h3 id="modificar-permisos">10.2. Cómo Modificar Permisos</h3>
    <p>Para modificar los permisos de un usuario:</p>
    <ol>
        <li>Acceder a la página de administración de permisos (admin_permisos_usuario.php)</li>
        <li>Seleccionar el usuario al que se desea modificar los permisos</li>
        <li>Marcar o desmarcar los permisos deseados</li>
        <li>Hacer clic en "Guardar Permisos"</li>
    </ol>

    <h3 id="actualizar-bd">10.3. Cómo Actualizar la Base de Datos</h3>
    <p>Para actualizar la estructura de la base de datos:</p>
    <ol>
        <li><strong>Crear un script de actualización:</strong>
            <p>Crear un archivo PHP con las consultas SQL necesarias para la actualización.</p>
        </li>
        <li><strong>Ejecutar el script:</strong>
            <p>Ejecutar el script desde el navegador o desde la línea de comandos.</p>
        </li>
        <li><strong>Verificar la actualización:</strong>
            <p>Verificar que la estructura de la base de datos se haya actualizado correctamente.</p>
        </li>
    </ol>

    <div class="footer">
        <p>Sistema de Reportes de Sueldos - Manual de Desarrollo</p>
        <p>Versión 1.0 - <?php echo date('Y'); ?></p>
    </div>
</body>
</html>
