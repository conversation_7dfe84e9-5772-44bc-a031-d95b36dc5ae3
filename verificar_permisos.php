<?php
// Incluir archivo de conexión y permisos
require_once 'conexion.php';
require_once 'permisos.php';

try {
    $pdo = getConexion();

    // Obtener todos los usuarios
    $sql = "SELECT id, username, nombre, rol FROM usuarios";
    $stmt = $pdo->query($sql);

    echo "<h2>Verificación de Permisos de Usuarios</h2>";

    while ($usuario = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $userId = $usuario['ID'];
        $username = $usuario['USERNAME'];
        $nombre = $usuario['NOMBRE'];
        $rol = $usuario['ROL'];

        echo "<h3>Usuario: $username (ID: $userId)</h3>";
        echo "Nombre: $nombre<br>";
        echo "Rol: $rol<br>";

        // Obtener permisos del usuario
        $permisosUsuario = [];

        // Verificar si hay permisos específicos en la tabla permisos_usuario
        try {
            $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
            $stmt2 = $pdo->prepare($sql);
            $stmt2->bindParam(':id_usuario', $userId);
            $stmt2->execute();

            if ($stmt2->rowCount() > 0) {
                $result = $stmt2->fetch(PDO::FETCH_ASSOC);
                $permisos = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];

                if (!empty($permisos)) {
                    $permisosUsuario = explode(',', $permisos);
                    echo "Permisos específicos: " . $permisos . "<br>";
                } else {
                    echo "No tiene permisos específicos asignados (cadena vacía)<br>";
                }
            } else {
                echo "No tiene permisos específicos asignados (no hay registro)<br>";
            }
        } catch (PDOException $e) {
            echo "Error al verificar permisos específicos: " . $e->getMessage() . "<br>";
        }

        // Obtener permisos del rol
        $permisosRoles = getPermisosRoles();
        if (isset($permisosRoles[$rol])) {
            echo "Permisos del rol $rol: " . implode(', ', $permisosRoles[$rol]) . "<br>";
        } else {
            echo "No hay permisos definidos para el rol $rol<br>";
        }

        // Verificar permisos específicos
        echo "<h4>Verificación de permisos específicos:</h4>";
        echo "<ul>";

        // Permisos a verificar
        $permisosVerificar = [
            'ver_reportes' => 'Ver todos los reportes',
            'exportar_excel' => 'Exportar datos a Excel',
            'admin_usuarios' => 'Administrar usuarios',
            'ver_reporte_falla_caja' => 'Ver reporte de Falla de Caja',
            'ver_reporte_san_nicolas' => 'Ver reporte de San Nicolás Mensual',
            'ver_reporte_santisimo_sacramento' => 'Ver reporte de Santísimo Sacramento Mensual',
            'ver_reporte_universidad_catolica' => 'Ver reporte de Universidad Católica Mensual',
            'ver_reporte_poder_judicial' => 'Ver reporte de Imponible Poder Judicial',
            'ver_reporte_reparto_poder_judicial' => 'Ver reporte de Reparto Poder Judicial',
            'ver_reporte_hidraulica' => 'Ver reporte de Imponible Hidráulica',
            'ver_consulta_hidraulica' => 'Ver consulta de Imponible Hidráulica (obsoleto)',
            'ver_consulta_policia' => 'Ver consulta de Imponible Policía',
            'ver_reporte_resumen_centros' => 'Ver reporte de Resumen por Centros',
            'ver_reporte_resumen_sectores' => 'Ver reporte de Resumen por Sectores'
        ];

        foreach ($permisosVerificar as $codigo => $descripcion) {
            // Simular la sesión para el usuario actual
            $_SESSION['user_id'] = $userId;
            $_SESSION['user_username'] = $username;
            $_SESSION['user_rol'] = $rol;

            $tiene = tienePermiso($codigo);
            echo "<li>$descripcion: " . ($tiene ? 'SÍ' : 'NO') . "</li>";
        }

        echo "</ul>";
        echo "<hr>";
    }

} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}
?>
