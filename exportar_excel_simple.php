<?php
// Incluir archivos necesarios
require_once 'conexion.php';
require_once 'permisos.php';
require_once 'auth.php';

// Verificar si el usuario está autenticado
requireLogin();

// Verificar si se ha especificado una consulta
if (!isset($_GET['consulta'])) {
    $_SESSION['error_exportar'] = 'No se ha especificado una consulta para exportar';
    header('Location: reportes.php');
    exit;
}

$consulta = $_GET['consulta'];

// Obtener el período seleccionado (mes y año)
$mesSeleccionado = isset($_GET['mes']) ? $_GET['mes'] : date('m');
$anioSeleccionado = isset($_GET['anio']) ? $_GET['anio'] : date('y');

// Validar mes (01-12)
if (!preg_match('/^(0[1-9]|1[0-2])$/', $mesSeleccionado)) {
    $mesSeleccionado = date('m');
}

// Validar a<PERSON> (00-99)
if (!preg_match('/^[0-9]{2}$/', $anioSeleccionado)) {
    $anioSeleccionado = date('y');
}

// Crear el sufijo de la tabla (MMAA)
$sufijo_tabla = $mesSeleccionado . $anioSeleccionado;

// Cargar las definiciones de consultas
require_once 'consultas_sql.php';

// Verificar que la consulta exista
if (!isset($consultas[$consulta])) {
    $_SESSION['error_exportar'] = 'La consulta especificada no existe';
    header('Location: reportes.php');
    exit;
}

// Verificar permiso para la consulta
if (!tienePermiso($consultas[$consulta]['permiso'])) {
    $_SESSION['error_permiso'] = 'No tienes permiso para exportar esta consulta';
    header('Location: reportes.php');
    exit;
}

try {
    error_log("Iniciando exportación a Excel para consulta: $consulta");
    $pdo = getConexion();

    // Obtener información de la consulta
    $tipoConsulta = isset($consultas[$consulta]['tipo']) ? $consultas[$consulta]['tipo'] : 'php';
    error_log("Tipo de consulta a ejecutar: $tipoConsulta");

    // Ejecutar la consulta según su tipo
    if ($tipoConsulta === 'sql_directo') {
        // Ejecutar la consulta SQL directamente
        $sql = $consultas[$consulta]['sql'];

        // Reemplazar las referencias a las tablas con el período seleccionado
        $sql = str_replace('sueldo0425', 'sueldo' . $sufijo_tabla, $sql);
        $sql = str_replace('ciescu0425', 'ciescu' . $sufijo_tabla, $sql);
        $sql = str_replace('imponi0425B', 'imponi' . $sufijo_tabla . 'B', $sql);
        $sql = str_replace('CIMENS0425', 'CIMENS' . $sufijo_tabla, $sql);
        $sql = str_replace('CIescu0425', 'CIescu' . $sufijo_tabla, $sql);
        $sql = str_replace('REPAR0425D', 'REPAR' . $sufijo_tabla . 'D', $sql);

        error_log("Ejecutando consulta SQL directa con período: $sufijo_tabla");

        // Ejecutar la consulta (SIN limitación para exportación completa)
        $stmt = $pdo->query($sql);
    } else {
        // Para consultas que requieren archivo
        $archivoConsulta = $consultas[$consulta]['archivo'];
        error_log("Archivo de consulta a ejecutar: $archivoConsulta (tipo: $tipoConsulta)");

        // Verificar que el archivo exista
        if (!file_exists($archivoConsulta)) {
            throw new Exception("El archivo de consulta no existe: $archivoConsulta");
        }

        if ($tipoConsulta === 'sql') {
            // Incluir el archivo para ejecutar scripts SQL
            require_once 'ejecutar_script_sql.php';

            // Ejecutar el script SQL
            $stmt = ejecutarScriptSQL($archivoConsulta);
        } else {
            // Crear un buffer para capturar la salida del archivo de consulta
            ob_start();

            // Incluir el archivo de consulta
            include $archivoConsulta;

            // Limpiar el buffer (para evitar que se muestre cualquier salida HTML)
            ob_end_clean();
        }
    }

    // Verificar que la variable $stmt esté definida
    if (!isset($stmt) || $stmt === null) {
        if ($tipoConsulta === 'sql_directo') {
            throw new Exception("La consulta SQL directa no generó resultados para: $consulta");
        } else {
            throw new Exception("La consulta no generó resultados. Verifica el archivo de consulta: " . (isset($archivoConsulta) ? $archivoConsulta : 'No definido'));
        }
    }

    error_log("Consulta ejecutada correctamente");

    // Obtener los nombres de las columnas
    $columnNames = [];
    $columnCount = $stmt->columnCount();
    error_log("Número de columnas en el resultado: $columnCount");

    for ($i = 0; $i < $columnCount; $i++) {
        $meta = $stmt->getColumnMeta($i);
        $columnNames[] = $meta['name'];
    }
    error_log("Nombres de columnas obtenidos: " . implode(', ', $columnNames));

    // Obtener los datos
    $data = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $data[] = $row;
    }
    error_log("Número de filas obtenidas: " . count($data));

    // Generar el archivo Excel en formato CSV
    $filename = 'reporte_' . $consulta . '_' . $sufijo_tabla . '_' . date('Ymd_His') . '.xls';

    // Asegurarse de que no haya salida previa
    if (ob_get_length()) {
        ob_end_clean();
    }

    // Configurar encabezados para descarga como Excel
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');

    // Crear un manejador de archivo para escribir en php://output
    $output = fopen('php://output', 'w');

    // Escribir el BOM (Byte Order Mark) para UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // Escribir los encabezados
    fputcsv($output, $columnNames, ';');

    // Escribir los datos
    foreach ($data as $row) {
        // Convertir valores numéricos a formato español (coma como separador decimal)
        foreach ($row as $key => $value) {
            if (is_numeric($value)) {
                $row[$key] = str_replace('.', ',', $value);
            }
        }
        fputcsv($output, $row, ';');
    }

    fclose($output);
    exit;

} catch (PDOException $e) {
    $error = 'Error en la conexión o la consulta: ' . $e->getMessage();
    error_log("Error PDO al exportar: " . $e->getMessage());

    // Mostrar información detallada para depuración
    if (isAdmin()) {
        echo "<h1>Error al exportar a Excel en formato CSV</h1>";
        echo "<p>Se ha producido un error al intentar exportar a Excel:</p>";
        echo "<pre>" . htmlspecialchars($error) . "</pre>";
        echo "<p>Rastreo de pila:</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        exit;
    }

    // Guardar el error en la sesión para mostrarlo después de la redirección
    $_SESSION['error_exportar'] = $error;
    header('Location: reportes.php?consulta=' . urlencode($consulta));
    exit;
} catch (Exception $e) {
    $error = 'Error al generar el archivo Excel en formato CSV: ' . $e->getMessage();
    error_log("Error general al exportar: " . $e->getMessage());

    // Mostrar información detallada para depuración
    if (isAdmin()) {
        echo "<h1>Error al exportar a Excel en formato CSV</h1>";
        echo "<p>Se ha producido un error al intentar exportar a Excel:</p>";
        echo "<pre>" . htmlspecialchars($error) . "</pre>";
        echo "<p>Rastreo de pila:</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        exit;
    }

    // Guardar el error en la sesión para mostrarlo después de la redirección
    $_SESSION['error_exportar'] = $error;
    header('Location: reportes.php?consulta=' . urlencode($consulta));
    exit;
}
?>
