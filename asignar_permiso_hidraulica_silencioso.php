<?php
// Incluir archivo de conexión y permisos
require_once 'conexion.php';
require_once 'permisos.php';

// Función para asignar permisos silenciosamente
function asignarPermisoHidraulicaSilencioso($userId) {
    try {
        $pdo = getConexion();

        // Verificar si el usuario es dpohidraulica
        $sql = "SELECT username FROM usuarios WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $userId);
        $stmt->execute();

        $esDpoHidraulica = false;
        if ($stmt->rowCount() > 0) {
            $userRow = $stmt->fetch(PDO::FETCH_ASSOC);
            $username = isset($userRow['USERNAME']) ? $userRow['USERNAME'] : $userRow['username'];
            if ($username === 'dpohidraulica') {
                $esDpoHidraulica = true;
                error_log("Usuario dpohidraulica detectado, asignando permisos especiales");
            }
        }

        // Permisos a asignar
        $permisos = [
            'exportar_excel',
            'ver_reporte_hidraulica',
        ];

        // Si es dpohidraulica, asegurarse de que tenga el permiso correcto
        if ($esDpoHidraulica) {
            error_log("Asignando permisos especiales para dpohidraulica");
        }

        // Verificar si ya existen permisos para este usuario
        $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id_usuario', $userId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            // Obtener permisos existentes
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $permisosExistentes = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];

            // Convertir a array
            $permisosArray = explode(',', $permisosExistentes);
            $permisosArray = array_map('trim', $permisosArray);

            // Agregar nuevos permisos sin duplicar
            $permisosModificados = false;
            foreach ($permisos as $permiso) {
                if (!in_array($permiso, $permisosArray)) {
                    $permisosArray[] = $permiso;
                    $permisosModificados = true;
                    error_log("Agregando permiso: $permiso");
                }
            }

            // Si no se modificaron los permisos, no hacer nada
            if (!$permisosModificados) {
                error_log("No se modificaron los permisos, ya están asignados");
                return true;
            }

            // Convertir de nuevo a string
            $permisosStr = implode(',', $permisosArray);
            error_log("Nuevos permisos: $permisosStr");

            // Actualizar permisos existentes
            $sql = "UPDATE permisos_usuario SET permisos = :permisos WHERE id_usuario = :id_usuario";
            error_log("Actualizando permisos existentes");
        } else {
            // Convertir el array de permisos a una cadena separada por comas
            $permisosStr = implode(',', $permisos);
            error_log("Permisos iniciales: $permisosStr");

            // Insertar nuevos permisos
            $sql = "INSERT INTO permisos_usuario (id_usuario, permisos) VALUES (:id_usuario, :permisos)";
            error_log("Insertando nuevos permisos");
        }

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id_usuario', $userId);
        $stmt->bindParam(':permisos', $permisosStr);

        if ($stmt->execute()) {
            error_log("Permisos guardados correctamente");

            // Limpiar la caché de permisos
            if (isset($_SESSION['permisos_cache'][$userId])) {
                unset($_SESSION['permisos_cache'][$userId]);
                error_log("Caché de permisos limpiada para el usuario");
            }
            if (isset($_SESSION['permisos_cache'])) {
                unset($_SESSION['permisos_cache']);
                error_log("Toda la caché de permisos ha sido limpiada");
            }
            return true;
        } else {
            $error = $stmt->errorInfo();
            error_log("Error al guardar los permisos: " . $error[2]);
            return false;
        }
    } catch (PDOException $e) {
        error_log("Error al asignar permisos silenciosamente: " . $e->getMessage());
        return false;
    }
}
?>
