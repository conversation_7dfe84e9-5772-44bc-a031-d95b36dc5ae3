<?php
// Incluir archivos necesarios
require_once 'conexion.php';
require_once 'permisos.php';
require_once 'auth.php';

// Verificar si el usuario está autenticado
requireLogin();

// Verificar si se ha especificado una consulta
if (!isset($_GET['consulta'])) {
    $_SESSION['error_exportar'] = 'No se ha especificado una consulta para exportar';
    header('Location: reportes.php');
    exit;
}

$consulta = $_GET['consulta'];

// Cargar las definiciones de consultas
require_once 'consultas_sql.php';

// Verificar que la consulta exista
if (!isset($consultas[$consulta])) {
    $_SESSION['error_exportar'] = 'La consulta especificada no existe';
    header('Location: reportes.php');
    exit;
}

// Verificar permiso para la consulta
if (!tienePermiso($consultas[$consulta]['permiso'])) {
    $_SESSION['error_permiso'] = 'No tienes permiso para exportar esta consulta';
    header('Location: reportes.php');
    exit;
}

try {
    error_log("Iniciando exportación a Excel para consulta: $consulta");
    $pdo = getConexion();

    // Obtener información de la consulta
    $archivoConsulta = $consultas[$consulta]['archivo'];
    $tipoConsulta = isset($consultas[$consulta]['tipo']) ? $consultas[$consulta]['tipo'] : 'php';
    error_log("Archivo de consulta a ejecutar: $archivoConsulta (tipo: $tipoConsulta)");

    // Verificar que el archivo exista
    if (!file_exists($archivoConsulta)) {
        throw new Exception("El archivo de consulta no existe: $archivoConsulta");
    }

    // Ejecutar la consulta según su tipo
    if ($tipoConsulta === 'sql') {
        // Incluir el archivo para ejecutar scripts SQL
        require_once 'ejecutar_script_sql.php';

        // Ejecutar el script SQL
        $stmt = ejecutarScriptSQL($archivoConsulta);
    } else if ($tipoConsulta === 'sql_directo') {
        // Ejecutar la consulta SQL directamente
        $sql = $consultas[$consulta]['sql'];

        // Ejecutar la consulta
        $stmt = $pdo->query($sql);
    } else {
        // Crear un buffer para capturar la salida del archivo de consulta
        ob_start();

        // Incluir el archivo de consulta
        include $archivoConsulta;

        // Limpiar el buffer (para evitar que se muestre cualquier salida HTML)
        ob_end_clean();
    }

    // Verificar que la variable $stmt esté definida
    if (!isset($stmt) || $stmt === null) {
        throw new Exception("La consulta no generó resultados. Verifica el archivo de consulta: $archivoConsulta");
    }

    error_log("Consulta ejecutada correctamente");

    // Incluir directamente los archivos de PHPExcel
    error_log("Cargando librerías PHPExcel");
    require_once 'PHPExcel.php';
    require_once 'PHPExcel/IOFactory.php';
    error_log("Librerías PHPExcel cargadas correctamente");

    // Crear un nuevo objeto PHPExcel
    $objPHPExcel = new PHPExcel();

    // Establecer propiedades del documento
    $objPHPExcel->getProperties()
        ->setCreator("Sistema de Reportes de Sueldos")
        ->setLastModifiedBy("Sistema de Reportes de Sueldos")
        ->setTitle($consultas[$consulta]['titulo'])
        ->setSubject($consultas[$consulta]['titulo'])
        ->setDescription($consultas[$consulta]['descripcion'])
        ->setKeywords("excel, reporte, " . $consulta)
        ->setCategory("Reportes");

    // Seleccionar la primera hoja
    $objPHPExcel->setActiveSheetIndex(0);
    $sheet = $objPHPExcel->getActiveSheet();

    // Establecer título de la hoja
    $sheet->setTitle($consultas[$consulta]['titulo']);

    // Obtener los nombres de las columnas
    $columnNames = [];
    $columnCount = $stmt->columnCount();
    error_log("Número de columnas en el resultado: $columnCount");

    for ($i = 0; $i < $columnCount; $i++) {
        $meta = $stmt->getColumnMeta($i);
        $columnNames[] = $meta['name'];
    }
    error_log("Nombres de columnas obtenidos: " . implode(', ', $columnNames));

    // Escribir los encabezados
    $col = 0;
    foreach ($columnNames as $header) {
        $sheet->setCellValueByColumnAndRow($col, 1, $header);
        $col++;
    }

    // Escribir los datos
    $row = 2;
    while ($fila = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $col = 0;
        foreach ($columnNames as $key) {
            $valor = isset($fila[$key]) ? $fila[$key] : '';
            $sheet->setCellValueByColumnAndRow($col, $row, $valor);
            $col++;
        }
        $row++;
    }

    // Generar el archivo Excel
    $filename = 'reporte_' . $consulta . '_' . date('Ymd_His') . '.xls';

    // Asegurarse de que no haya salida previa
    if (ob_get_length()) {
        ob_end_clean();
    }

    // Configurar encabezados para descarga
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');

    // Crear un escritor de Excel
    $writer = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');

    // Guardar el archivo directamente en la salida
    $writer->save('php://output');
    exit;

} catch (PDOException $e) {
    $error = 'Error en la conexión o la consulta: ' . $e->getMessage();
    error_log("Error PDO al exportar: " . $e->getMessage());

    // Mostrar información detallada para depuración
    if (isAdmin()) {
        echo "<h1>Error al exportar a Excel</h1>";
        echo "<p>Se ha producido un error al intentar exportar a Excel:</p>";
        echo "<pre>" . htmlspecialchars($error) . "</pre>";
        echo "<p>Rastreo de pila:</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        exit;
    }

    // Guardar el error en la sesión para mostrarlo después de la redirección
    $_SESSION['error_exportar'] = $error;
    header('Location: reportes.php?consulta=' . urlencode($consulta));
    exit;
} catch (Exception $e) {
    $error = 'Error al generar el archivo Excel: ' . $e->getMessage();
    error_log("Error general al exportar: " . $e->getMessage());

    // Mostrar información detallada para depuración
    if (isAdmin()) {
        echo "<h1>Error al exportar a Excel</h1>";
        echo "<p>Se ha producido un error al intentar exportar a Excel:</p>";
        echo "<pre>" . htmlspecialchars($error) . "</pre>";
        echo "<p>Rastreo de pila:</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        exit;
    }

    // Guardar el error en la sesión para mostrarlo después de la redirección
    $_SESSION['error_exportar'] = $error;
    header('Location: reportes.php?consulta=' . urlencode($consulta));
    exit;
}
?>
