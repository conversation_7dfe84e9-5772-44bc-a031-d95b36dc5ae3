<?php
// Incluir archivo de conexión
require_once 'conexion.php';

// Probar diferentes opciones de conexión
$resultados = probarConexiones();

// Mostrar resultados
echo "<h1>Resultados de pruebas de conexión</h1>";
echo "<ul>";
foreach ($resultados as $tipo => $resultado) {
    echo "<li><strong>$tipo:</strong> $resultado</li>";
}
echo "</ul>";

// Mostrar información de configuración
echo "<h2>Configuración actual</h2>";
echo "<ul>";
echo "<li><strong>Host:</strong> $host</li>";
echo "<li><strong>Puerto:</strong> $port</li>";
echo "<li><strong>Servicio:</strong> $service</li>";
echo "<li><strong>SID:</strong> $sid</li>";
echo "<li><strong>Usuario:</strong> $user</li>";
echo "</ul>";

// Mostrar cadenas de conexión
echo "<h2>Cadenas de conexión</h2>";
echo "<ul>";
echo "<li><strong>Service Name:</strong> $dsn_service</li>";
echo "<li><strong>SID:</strong> $dsn_sid</li>";
echo "<li><strong>TNS:</strong> $dsn_tns</li>";
echo "<li><strong>Formato completo:</strong> $dsn_full</li>";
echo "</ul>";

// Mostrar información de PHP y extensiones
echo "<h2>Información de PHP</h2>";
echo "<ul>";
echo "<li><strong>Versión de PHP:</strong> " . phpversion() . "</li>";
echo "<li><strong>Extensión OCI8:</strong> " . (extension_loaded('oci8') ? 'Cargada' : 'No cargada') . "</li>";
echo "<li><strong>Extensión PDO_OCI:</strong> " . (extension_loaded('pdo_oci') ? 'Cargada' : 'No cargada') . "</li>";
echo "</ul>";

// Mostrar variables de entorno relacionadas con Oracle
echo "<h2>Variables de entorno Oracle</h2>";
echo "<ul>";
echo "<li><strong>ORACLE_HOME:</strong> " . (getenv('ORACLE_HOME') ?: 'No definida') . "</li>";
echo "<li><strong>TNS_ADMIN:</strong> " . (getenv('TNS_ADMIN') ?: 'No definida') . "</li>";
echo "<li><strong>LD_LIBRARY_PATH:</strong> " . (getenv('LD_LIBRARY_PATH') ?: 'No definida') . "</li>";
echo "</ul>";

// Sugerencias para solucionar problemas
echo "<h2>Sugerencias para solucionar problemas</h2>";
echo "<ol>";
echo "<li>Verifica que el servicio Oracle esté en ejecución en el servidor.</li>";
echo "<li>Comprueba que el nombre del servicio o SID sea correcto.</li>";
echo "<li>Asegúrate de que el listener de Oracle esté configurado correctamente.</li>";
echo "<li>Verifica que el firewall no esté bloqueando la conexión.</li>";
echo "<li>Comprueba las credenciales de usuario y contraseña.</li>";
echo "<li>Verifica que las extensiones OCI8 y PDO_OCI estén instaladas y habilitadas en PHP.</li>";
echo "</ol>";

// Formulario para probar una conexión personalizada
echo "<h2>Probar conexión personalizada</h2>";
echo "<form method='post'>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='host'>Host:</label>";
echo "<input type='text' id='host' name='host' value='$host'>";
echo "</div>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='port'>Puerto:</label>";
echo "<input type='text' id='port' name='port' value='$port'>";
echo "</div>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='service'>Servicio/SID:</label>";
echo "<input type='text' id='service' name='service' value='$service'>";
echo "</div>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='user'>Usuario:</label>";
echo "<input type='text' id='user' name='user' value='$user'>";
echo "</div>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='pass'>Contraseña:</label>";
echo "<input type='password' id='pass' name='pass' value='$pass'>";
echo "</div>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label for='tipo'>Tipo de conexión:</label>";
echo "<select id='tipo' name='tipo'>";
echo "<option value='service'>Service Name</option>";
echo "<option value='sid' selected>SID</option>";
echo "<option value='tns'>TNS</option>";
echo "<option value='full'>Formato completo</option>";
echo "</select>";
echo "</div>";
echo "<button type='submit' name='probar'>Probar conexión</button>";
echo "</form>";

// Procesar formulario
if (isset($_POST['probar'])) {
    $host_form = $_POST['host'];
    $port_form = $_POST['port'];
    $service_form = $_POST['service'];
    $user_form = $_POST['user'];
    $pass_form = $_POST['pass'];
    $tipo = $_POST['tipo'];
    
    // Construir cadena de conexión según el tipo seleccionado
    switch ($tipo) {
        case 'service':
            $dsn_form = "oci:dbname=//{$host_form}:{$port_form}/{$service_form};charset=AL32UTF8";
            break;
        case 'sid':
            $dsn_form = "oci:dbname={$host_form}:{$port_form}/{$service_form};charset=AL32UTF8";
            break;
        case 'tns':
            $dsn_form = "oci:dbname={$service_form}";
            break;
        case 'full':
            $dsn_form = "oci:dbname=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST={$host_form})(PORT={$port_form}))(CONNECT_DATA=(SERVICE_NAME={$service_form})))";
            break;
    }
    
    // Probar conexión
    try {
        $pdo = new PDO($dsn_form, $user_form, $pass_form);
        echo "<div style='margin-top: 20px; padding: 10px; background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb;'>";
        echo "<strong>Conexión exitosa!</strong> La conexión a la base de datos se ha establecido correctamente.";
        echo "</div>";
    } catch (PDOException $e) {
        echo "<div style='margin-top: 20px; padding: 10px; background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;'>";
        echo "<strong>Error de conexión:</strong> " . $e->getMessage();
        echo "</div>";
    }
}
?>
