<?php
// Incluir archivo de conexión y permisos
require_once 'conexion.php';
require_once 'permisos.php';

// Verificar que el usuario esté autenticado
requireLogin();
requireAdmin();

// Obtener el nombre de usuario de la URL o usar el valor predeterminado
$username = isset($_GET['username']) ? $_GET['username'] : 'dpohidraulica';

try {
    $pdo = getConexion();

    // Buscar el ID del usuario
    $sql = "SELECT id FROM usuarios WHERE username = :username";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':username', $username);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $usuario = $stmt->fetch(PDO::FETCH_ASSOC);
        $userId = $usuario['ID'];

        echo "<h2>Asignando permisos al usuario $username (ID: $userId)</h2>";

        // Permisos a asignar
        $permisos = [
            'exportar_excel',
            'ver_reporte_hidraulica',
            '
        ];

        // Convertir el array de permisos a una cadena separada por comas
        $permisosStr = implode(',', $permisos);

        // Verificar si ya existen permisos para este usuario
        $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id_usuario', $userId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            // Obtener permisos existentes
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $permisosExistentes = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];
            
            echo "<p>Permisos existentes: $permisosExistentes</p>";
            
            // Convertir a array
            $permisosArray = explode(',', $permisosExistentes);
            
            // Agregar nuevos permisos sin duplicar
            foreach ($permisos as $permiso) {
                if (!in_array($permiso, $permisosArray)) {
                    $permisosArray[] = $permiso;
                }
            }
            
            // Convertir de nuevo a string
            $permisosStr = implode(',', $permisosArray);
            
            // Actualizar permisos existentes
            $sql = "UPDATE permisos_usuario SET permisos = :permisos WHERE id_usuario = :id_usuario";
            echo "<p>Actualizando permisos existentes...</p>";
        } else {
            // Insertar nuevos permisos
            $sql = "INSERT INTO permisos_usuario (id_usuario, permisos) VALUES (:id_usuario, :permisos)";
            echo "<p>Insertando nuevos permisos...</p>";
        }

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id_usuario', $userId);
        $stmt->bindParam(':permisos', $permisosStr);

        if ($stmt->execute()) {
            echo "<p style='color: green;'>Permisos asignados correctamente: $permisosStr</p>";

            // Verificar que los permisos se hayan guardado correctamente
            $sql = "SELECT permisos FROM permisos_usuario WHERE id_usuario = :id_usuario";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':id_usuario', $userId);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $permisosGuardados = isset($result['PERMISOS']) ? $result['PERMISOS'] : $result['permisos'];
                echo "<p>Permisos guardados en la base de datos: $permisosGuardados</p>";
                
                // Limpiar la caché de permisos
                echo "<p>Limpiando caché de permisos...</p>";
                if (isset($_SESSION['permisos_cache'][$userId])) {
                    unset($_SESSION['permisos_cache'][$userId]);
                }
                if (isset($_SESSION['permisos_cache'])) {
                    unset($_SESSION['permisos_cache']);
                }
                
                echo "<p style='margin-top: 20px;'><a href='reportes.php' style='padding: 10px 15px; background-color: #3498db; color: white; text-decoration: none; border-radius: 4px;'>Ir a la página de reportes</a></p>";
            } else {
                echo "<p style='color: red;'>No se encontraron permisos guardados para el usuario $userId</p>";
            }
        } else {
            $error = $stmt->errorInfo();
            echo "<p style='color: red;'>Error al guardar los permisos: " . $error[2] . "</p>";
        }
    } else {
        echo "<p style='color: red;'>No se encontró el usuario $username</p>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
