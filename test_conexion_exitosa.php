<?php
// Establecer el modo de visualización de errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Prueba de Conexión a Oracle</h1>";

// Mostrar información de PHP
echo "<h2>Información de PHP</h2>";
echo "<ul>";
echo "<li><strong>Versión de PHP:</strong> " . phpversion() . "</li>";
echo "<li><strong>Extensiones cargadas:</strong> ";
$extensions = get_loaded_extensions();
sort($extensions);
echo implode(', ', $extensions);
echo "</li>";
echo "</ul>";

// Verificar extensiones de Oracle
echo "<h2>Extensiones de Oracle</h2>";
echo "<ul>";
echo "<li><strong>OCI8:</strong> " . (extension_loaded('oci8') ? '<span style="color:green">Cargada</span>' : '<span style="color:red">No cargada</span>') . "</li>";
echo "<li><strong>PDO_OCI:</strong> " . (extension_loaded('pdo_oci') ? '<span style="color:green">Cargada</span>' : '<span style="color:red">No cargada</span>') . "</li>";
echo "</ul>";

// Probar diferentes formatos de conexión
echo "<h2>Prueba de Conexiones</h2>";

// Configuración de la conexión
$host = '**********';
$port = '1521';
$service = 'DESA920';
$sid = 'DESA920';
$user = 'EXP5';
$pass = 'E*x20_22.';

// Diferentes formatos de DSN
$dsn_formats = [
    'Easy Connect (SID)' => "oci:dbname={$host}:{$port}/{$sid}",
    'Easy Connect (Service Name)' => "oci:dbname=//{$host}:{$port}/{$service}",
    'TNS' => "oci:dbname=DESA920",
    'SID tradicional' => "oci:dbname={$host}:{$port}:{$sid}",
    'Formato completo' => "oci:dbname=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST={$host})(PORT={$port}))(CONNECT_DATA=(SERVICE_NAME={$service})))"
];

echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Formato</th><th>DSN</th><th>Resultado</th></tr>";

foreach ($dsn_formats as $format => $dsn) {
    echo "<tr>";
    echo "<td><strong>{$format}</strong></td>";
    echo "<td>{$dsn}</td>";
    
    try {
        $pdo = new PDO($dsn, $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Ejecutar una consulta simple para verificar la conexión
        $stmt = $pdo->query("SELECT 'Conexión exitosa' AS MENSAJE FROM DUAL");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<td style='color:green'>✓ " . $result['MENSAJE'] . "</td>";
    } catch (PDOException $e) {
        echo "<td style='color:red'>✗ Error: " . $e->getMessage() . "</td>";
    }
    
    echo "</tr>";
}

echo "</table>";

// Probar la conexión usando el archivo CONEXION.PHP
echo "<h2>Prueba de Conexión usando CONEXION.PHP</h2>";

try {
    // Incluir el archivo de conexión
    require_once 'CONEXION.PHP';
    
    // Obtener conexión
    $pdo = getConexion();
    
    // Ejecutar una consulta simple
    $stmt = $pdo->query("SELECT 'Conexión exitosa usando CONEXION.PHP' AS MENSAJE FROM DUAL");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div style='padding: 10px; background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; border-radius: 4px; margin-bottom: 20px;'>";
    echo "<strong>✓ Éxito:</strong> " . $result['MENSAJE'];
    echo "</div>";
    
    // Mostrar información de la conexión
    echo "<h3>Información de la Conexión</h3>";
    echo "<ul>";
    echo "<li><strong>Versión del servidor:</strong> " . $pdo->getAttribute(PDO::ATTR_SERVER_VERSION) . "</li>";
    echo "<li><strong>Versión del cliente:</strong> " . $pdo->getAttribute(PDO::ATTR_CLIENT_VERSION) . "</li>";
    echo "<li><strong>Estado de la conexión:</strong> " . $pdo->getAttribute(PDO::ATTR_CONNECTION_STATUS) . "</li>";
    echo "</ul>";
    
    // Probar una consulta más compleja
    echo "<h3>Prueba de Consulta Compleja</h3>";
    
    try {
        $stmt = $pdo->query("SELECT * FROM USER_TABLES WHERE ROWNUM <= 10");
        $tables = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($tables) > 0) {
            echo "<p>Se encontraron " . count($tables) . " tablas:</p>";
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>" . $table['TABLE_NAME'] . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No se encontraron tablas.</p>";
        }
    } catch (PDOException $e) {
        echo "<div style='padding: 10px; background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 4px; margin-bottom: 20px;'>";
        echo "<strong>✗ Error en la consulta:</strong> " . $e->getMessage();
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<div style='padding: 10px; background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 4px; margin-bottom: 20px;'>";
    echo "<strong>✗ Error de conexión:</strong> " . $e->getMessage();
    echo "</div>";
    
    // Sugerencias para solucionar el problema
    echo "<h3>Sugerencias para Solucionar el Problema</h3>";
    echo "<ol>";
    echo "<li>Verifica que el servidor Oracle esté en ejecución.</li>";
    echo "<li>Comprueba que las credenciales de usuario y contraseña sean correctas.</li>";
    echo "<li>Asegúrate de que el nombre del servicio o SID sea correcto.</li>";
    echo "<li>Verifica que el firewall no esté bloqueando la conexión.</li>";
    echo "<li>Comprueba que las extensiones OCI8 y PDO_OCI estén instaladas y habilitadas en PHP.</li>";
    echo "</ol>";
}

// Mostrar el contenido del archivo CONEXION.PHP
echo "<h2>Contenido del Archivo CONEXION.PHP</h2>";
echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #ddd; border-radius: 4px; overflow: auto;'>";
highlight_file('CONEXION.PHP');
echo "</pre>";
?>
