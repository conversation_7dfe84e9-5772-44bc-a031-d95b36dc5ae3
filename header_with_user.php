<div style="position: fixed; top: 20px; right: 20px; z-index: 9999; background-color: #2c3e50; color: white; padding: 8px 15px; border-radius: 4px; font-weight: bold; display: flex; align-items: center; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
    <div style="width: 28px; height: 28px; background-color: <?php echo $_SESSION['user_rol'] === 'admin' ? '#3498db' : '#27ae60'; ?>; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 10px;">
        <?php if ($_SESSION['user_rol'] === 'admin'): ?>A<?php else: ?>U<?php endif; ?>
    </div>
    <div>
        <?php echo htmlspecialchars($_SESSION['user_nombre'] ?? $_SESSION['user_username']); ?>
        <span style="margin-left: px; opacity: 0.8; font-size: 0.9em;">(<?php echo $_SESSION['user_rol'] === 'admin' ? 'ADMINISTRADOR' : 'USUARIO'; ?>)</span>
    </div>
</div>

<header>
    <div class="container" style="display: flex; justify-content: left; align-items: left; padding: 9px 0;">
        <img src="images/logo.png" alt="Logo del Sistema" style="max-height: 120px; max-width: 100%;">
    </div>
</header>
