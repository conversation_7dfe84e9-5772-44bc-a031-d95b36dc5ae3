<?php
// Incluir archivo de conexión
require_once 'conexion.php';

try {
    $pdo = getConexion();

    // Crear tabla de usuarios si no existe
    $sql = "
    BEGIN
        EXECUTE IMMEDIATE 'DROP TABLE usuarios';
        EXCEPTION WHEN OTHERS THEN NULL;
    END;
    ";
    $pdo->exec($sql);

    // Crear secuencia para el ID
    $sql = "
    BEGIN
        EXECUTE IMMEDIATE 'DROP SEQUENCE usuarios_seq';
        EXCEPTION WHEN OTHERS THEN NULL;
    END;
    ";
    $pdo->exec($sql);

    $sql = "CREATE SEQUENCE usuarios_seq START WITH 1 INCREMENT BY 1";
    $pdo->exec($sql);

    // Especificar el tablespace a utilizar
    $tablespace = "SUELDOS27"; // Tablespace especificado por el usuario

    // Crear tabla de usuarios con el tablespace especificado
    $sql = "
    CREATE TABLE usuarios (
        id NUMBER PRIMARY KEY,
        username VARCHAR2(50) NOT NULL UNIQUE,
        password VARCHAR2(255) NOT NULL,
        nombre VARCHAR2(100) NOT NULL,
        email VARCHAR2(100) NOT NULL,
        rol VARCHAR2(20) NOT NULL,
        fecha_creacion DATE DEFAULT SYSDATE,
        activo NUMBER(1) DEFAULT 1
    ) TABLESPACE " . $tablespace;
    $pdo->exec($sql);

    // Crear trigger para autoincrement
    $sql = "
    BEGIN
        EXECUTE IMMEDIATE 'DROP TRIGGER usuarios_trg';
        EXCEPTION WHEN OTHERS THEN NULL;
    END;
    ";
    $pdo->exec($sql);

    $sql = "
    CREATE OR REPLACE TRIGGER usuarios_trg
    BEFORE INSERT ON usuarios
    FOR EACH ROW
    BEGIN
        SELECT usuarios_seq.NEXTVAL INTO :new.id FROM dual;
    END;
    ";
    $pdo->exec($sql);

    // Insertar usuario administrador por defecto
    $username = 'admin';
    $password = password_hash('admin1234', PASSWORD_DEFAULT);
    $nombre = 'Administrador';
    $email = '<EMAIL>';
    $rol = 'admin';

    $sql = "INSERT INTO usuarios (username, password, nombre, email, rol) VALUES (:username, :password, :nombre, :email, :rol)";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':username', $username);
    $stmt->bindParam(':password', $password);
    $stmt->bindParam(':nombre', $nombre);
    $stmt->bindParam(':email', $email);
    $stmt->bindParam(':rol', $rol);
    $stmt->execute();

    echo "Tabla de usuarios creada correctamente y usuario administrador insertado.<br>";
    echo "Usuario: admin<br>";
    echo "Contraseña: admin1234<br>";

} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
