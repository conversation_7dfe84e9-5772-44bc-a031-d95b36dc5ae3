<?php
/**
 * Definición de consultas SQL para el sistema de reportes
 *
 * Este archivo define todas las consultas y reportes disponibles en el sistema,
 * junto con sus permisos, categorías, títulos y descripciones.
 * Las consultas apuntan a scripts SQL en lugar de tener el SQL embebido.
 */

// Definir las categorías de consultas
$categorias = [
    'sin_ajuste' => [
        'titulo' => 'Sin Ajuste',
        'descripcion' => 'Consultas sin aplicar ajustes'
    ],
    'con_ajuste' => [
        'titulo' => 'Con Ajuste',
        'descripcion' => 'Consultas con ajustes aplicados'
    ]
];

// Definir las consultas disponibles
$consultas = [
    // Categoría: Sin Ajuste
    'falla_caja' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'FALLA DE CAJA',
        'descripcion' => 'Consulta de datos de falla de caja.',
        'archivo' => 'scripts_sql/falla_caja.sql',
        'tipo' => 'sql',
        'permiso' => 'ver_reporte_falla_caja'
    ],
    'san_nicolas_mensual' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'COLEGIO SAN NICOLAS MENSUAL',
        'descripcion' => 'Consulta mensual del Colegio San Nicolás con PIVOT dinámico. Incluye información detallada de empleados y conceptos usando códigos del centro 67, sector 978.',
        'sql' => "SELECT
            C.NOMMINISTERIO as \"MINISTERIO\",
            TO_CHAR(A.CENTRO, '00') as \"CENTRO\",
            C.nomcentro as \"NOMBRE_CENTRO\",
            TO_CHAR(A.sector, '000') as \"SECTOR\",
            C.nomsector as \"NOMBRE_SECTOR\",
            TO_CHAR(A.padrond,'0000000') as \"PADRON\",
            A.CUIL as \"CUIL\",
            A.APYNOM as \"APELLIDO_Y_NOMBRE\",
            A.REGIMEN as \"REGIMEN\",
            A.ESCALAFON as \"ESCALAFON\",
            A.CATEG as \"CATEGORIA\",
            A.AG_TRAMO as \"AGRUPACION_POR_TRAMO\",
            A.ANTIG as \"ANTIGUEDAD\",
            A.NETO as \"NETO\",
            A.TACAREDU as \"TOTAL_ASIG_CON_APORT\",
            A.TASAREDU as \"TOTAL_ASIG_SIN_APORT\",
            B.A01, B.A02, B.A03, B.A06, B.A38, B.A76, B.A79, B.C01, B.E20, B.E30, B.E97, B.F26, B.G08, B.G16, B.G30, B.H01, B.H02, B.H03, B.H04, B.H08, B.H40, B.H52, B.I02, B.I14, B.I26, B.I27, B.I35, B.I36, B.I37, B.I85, B.J02, B.J06, B.J11, B.J12, B.J15, B.J16, B.J18, B.J23, B.J39, B.J46, B.J55, B.J60, B.J63, B.J64, B.J65, B.J71, B.J82, B.J83, B.J92, B.J98, B.K03, B.K09, B.K18, B.K47, B.K63, B.K81, B.K98, B.L01, B.L02, B.P10, B.P11, B.P17, B.P20, B.P33, B.P36, B.P61, B.P72, B.Q11, B.Q40, B.Z01, B.Z02, B.Z52
        FROM exp5.SUELDO0425 A
        JOIN exp5.REPARTICIONES C
        ON A.CENTRO = C.CENTRO AND A.SECTOR = C.SECTOR AND A.CENTRO IN ('36')
        LEFT JOIN (
            SELECT * FROM (
                SELECT CENTRO, padrond, codigo, importe FROM EXP5.CIMENS0425
            )
            PIVOT (
                sum(importe)
                FOR codigo IN (
                    'A01' A01, 'A02' A02, 'A03' A03, 'A06' A06, 'A38' A38, 'A76' A76, 'A79' A79, 'C01' C01, 'E20' E20, 'E30' E30, 'E97' E97, 'F26' F26, 'G08' G08, 'G16' G16, 'G30' G30, 'H01' H01, 'H02' H02, 'H03' H03, 'H04' H04, 'H08' H08, 'H40' H40, 'H52' H52, 'I02' I02, 'I14' I14, 'I26' I26, 'I27' I27, 'I35' I35, 'I36' I36, 'I37' I37, 'I85' I85, 'J02' J02, 'J06' J06, 'J11' J11, 'J12' J12, 'J15' J15, 'J16' J16, 'J18' J18, 'J23' J23, 'J39' J39, 'J46' J46, 'J55' J55, 'J60' J60, 'J63' J63, 'J64' J64, 'J65' J65, 'J71' J71, 'J82' J82, 'J83' J83, 'J92' J92, 'J98' J98, 'K03' K03, 'K09' K09, 'K18' K18, 'K47' K47, 'K63' K63, 'K81' K81, 'K98' K98, 'L01' L01, 'L02' L02, 'P10' P10, 'P11' P11, 'P17' P17, 'P20' P20, 'P33' P33, 'P36' P36, 'P61' P61, 'P72' P72, 'Q11' Q11, 'Q40' Q40, 'Z01' Z01, 'Z02' Z02, 'Z52' Z52
                )
            )
        ) B ON b.padrond = a.padrond
        ORDER BY A.CENTRO, A.SECTOR, A.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reporte_san_nicolas'
    ],
    'santisimo_sacramento_mensual' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'Santísimo Sacramento Mensual',
        'descripcion' => 'Consulta mensual de Santísimo Sacramento.',
        'archivo' => 'scripts_sql/santisimo_sacramento.sql',
        'tipo' => 'sql',
        'permiso' => 'ver_reporte_santisimo_sacramento'
    ],
    'universidad_catolica_mensual' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'Universidad Católica Mensual',
        'descripcion' => 'Consulta mensual de Universidad Católica.',
        'archivo' => 'scripts_sql/universidad_catolica.sql',
        'tipo' => 'sql',
        'permiso' => 'ver_reporte_universidad_catolica'
    ],
    'imponible_poder_judicial' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'PODER JUDICIAL - IMPONIBLE',
        'descripcion' => 'Consulta de imponible para el Poder Judicial. Incluye información detallada de empleados y conceptos imponibles.',
        'sql' => "SELECT
            a.CENTRO as \"CENTRO\",
            a.SECTOR as \"SECTOR\",
            a.PADROND as \"PADRON\",
            a.apynom as \"NOMBRE\",
            a.cuil as \"CUIL\",
            a.antig as \"ANTIGUEDAD\",
            a.perm_cat as \"PERM_CAT\",
            a.categ as \"CATEGORIA\",
            a.escalafon as \"ESCALAFON\",
            a.fnacim as \"FNACIM\",
            b.ASIGAPOR as \"ASIGAPOR\",
            b.RETROMESANT as \"RETROMESANT\",
            b.OTRORETRO as \"OTRORETRO\",
            b.OTROCONCEPSINAPO as \"OTROCONCEPSINAPO\",
            b.SALARIOSINAPO as \"SALARIOSINAPO\",
            b.APORJUB as \"APORJUB\",
            b.H01 as \"H01\",
            b.Z01 as \"Z01\",
            b.H31 as \"H31\",
            b.H43 as \"H43\",
            b.H70 as \"H70\",
            b.H52 as \"H52\",
            b.Z52 as \"Z52\"
        FROM sueldo0425 a, imponi0425B b
        WHERE a.padrond = b.padrond AND a.centro = 43
        ORDER BY a.CENTRO, a.sector, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reporte_poder_judicial'
    ],
    'reparto_poder_judicial' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'PODER JUDICIAL - REPARTO',
        'descripcion' => 'Consulta de reparto para el Poder Judicial. Muestra todos los datos de la tabla REPAR.',
        'sql' => "SELECT * FROM REPAR0425D",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reporte_reparto_poder_judicial'
    ],

    // Reportes adicionales de consultas.php en categoría "Sin Ajuste"
    'falla_caja_original' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'Falla de Caja (Original)',
        'descripcion' => 'Consulta de datos de falla de caja (versión original).',
        'sql' => "select a.centro,C.NOMCENTRO,a.sector,C.NOMSECTOR,a.padrond,a.cuil,a.apynom,a.categ,
A.ESCALAFON,A.TACAREDU,
NVL(TO_CHAR(b.A05),'0,00') AS A05,
NVL(TO_CHAR(b.A79),'0,00') AS A79
--Nvl(A05, 0) + Nvl(A79, 0) Suma
from sueldo0425 a
join
(

SELECT TO_CHAR (a.CENTRO, '00') CENTRO,TO_CHAR (A.sector, '000') SECTOR,TO_CHAR(A.padrond,'0000000') padron,B.*
FROM exp5.sueldo0425 A
join
(
select * from (
select padronD,codigo,importe from EXP5.CIESCU0425
)
PIVOT
(
    sum(importe)
    FOR codigo IN (
'A05' A05,
'A79' A79
))
)
B ON (b.padrond=a.padrond) --(B.Q38>0 OR B.Q39>0 OR B.Q40>0 OR B.Q62>0 OR B.Q63>0);--; ---and a01>0 and a27>0;
UNION
SELECT TO_CHAR (a.CENTRO, '00') CENTRO,TO_CHAR (A.sector, '000') SECTOR,TO_CHAR(A.padrond,'0000000') padron,B.*
FROM exp5.sueldo0425 A
join
(
select * from (
select padronD,codigo,importe from EXP5.CIMENS0425
)
PIVOT
(
    sum(importe)
    FOR codigo IN (
'A05' A05,
'A79' A79
))
)
B ON (b.padrond=a.padrond) --(B.Q38>0 OR B.Q39>0 OR B.Q40>0 OR B.Q62>0 OR B.Q63>0);--; ---and a01>0 and a27>0;
)


B ON (b.padrond=a.padrond)  AND (B.A05>0 OR B.A79>0 OR B.A05<0 OR B.A79<0 )

JOIN
REPARTICIONES C
ON a.padrond=b.padrond AND A.CENTRO=C.CENTRO AND A.SECTOR=C.SECTOR",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reporte_falla_caja'
    ],
    'san_nicolas_original' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'San Nicolás Mensual (Original)',
        'descripcion' => 'Consulta mensual de San Nicolás (versión original).',
        'sql' => "select a.centro,a.sector,a.padrond,a.apynom,a.cuil,a.planta,a.escalafon,a.categ,a.antig,a.tacaredu,
a.tasaredu,a.totdesc,a.neto,b.codigo,b.importe
from sueldo0425 a,ciescu0425 b
where a.padrond=b.padrond and a.centro=63 and (a.sector=419 OR a.sector=377)
order by a.padrond,codigo",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reporte_san_nicolas'
    ],
    'santisimo_sacramento_original' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'Santísimo Sacramento Mensual (Original)',
        'descripcion' => 'Consulta mensual de Santísimo Sacramento (versión original).',
        'sql' => "select a.centro,a.sector,a.padrond,a.apynom,a.cuil,a.planta,a.escalafon,a.categ,a.antig,a.tacaredu,
a.tasaredu,a.totdesc,a.neto,b.codigo,b.importe
from sueldo0425 a,ciescu0425 b
where a.padrond=b.padrond and a.centro=64 and a.sector=443
UNION
select a.centro,a.sector,a.padrond,a.apynom,a.cuil,a.planta,a.escalafon,a.categ,a.antig,a.tacaredu,
a.tasaredu,a.totdesc,a.neto,b.codigo,b.importe
from sueldo0425 a,ciescu0425 b
where a.padrond=b.padrond and a.centro=63 and a.sector=443
UNION
select a.centro,a.sector,a.padrond,a.apynom,a.cuil,a.planta,a.escalafon,a.categ,a.antig,a.tacaredu,
a.tasaredu,a.totdesc,a.neto,b.codigo,b.importe
from sueldo0425 a,ciescu0425 b
where a.padrond=b.padrond and a.centro=29 and a.sector=916
UNION
select a.centro,a.sector,a.padrond,a.apynom,a.cuil,a.planta,a.escalafon,a.categ,a.antig,a.tacaredu,
a.tasaredu,a.totdesc,a.neto,b.codigo,b.importe
from sueldo0425 a,ciescu0425 b
where a.padrond=b.padrond and a.centro=67 and a.sector=916
order by padrond,codigo",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reporte_santisimo_sacramento'
    ],
    'universidad_catolica_original' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'Universidad Católica Mensual (Original)',
        'descripcion' => 'Consulta mensual de Universidad Católica (versión original).',
        'sql' => "SELECT
                    a.CENTRO,
                    a.SECTOR,
                    a.PADROND,
                    a.APYNOM,
                    a.CUIL,
                    a.CATEG,
                    a.ESCALAFON,
                    ASIGAPOR,
                    RETROMESANT,
                    OTRORETRO,
                    SALARIOSINAPO,
                    APORJUB
                FROM sueldo0425 a
                JOIN imponi0425B b ON a.PADROND = b.PADROND
                WHERE a.CENTRO = 25
                ORDER BY a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reporte_universidad_catolica'
    ],
    'imponible_poder_judicial_original' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'Imponible Poder Judicial (Original)',
        'descripcion' => 'Consulta de imponible para el Poder Judicial (versión original).',
        'sql' => "SELECT
                    a.CENTRO,
                    a.SECTOR,
                    a.PADROND,
                    a.APYNOM,
                    a.CUIL,
                    a.CATEG,
                    a.ESCALAFON,
                    ASIGAPOR,
                    RETROMESANT,
                    OTRORETRO,
                    SALARIOSINAPO,
                    APORJUB
                FROM sueldo0425 a
                JOIN imponi0425B b ON a.PADROND = b.PADROND
                WHERE a.CENTRO = 26
                ORDER BY a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reporte_poder_judicial'
    ],
    'reparto_poder_judicial_original' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'Reparto Poder Judicial (Original)',
        'descripcion' => 'Consulta de reparto para el Poder Judicial (versión original).',
        'sql' => "SELECT
                    a.CENTRO,
                    a.SECTOR,
                    a.PADROND,
                    a.APYNOM,
                    a.CUIL,
                    a.CATEG,
                    a.ESCALAFON,
                    ASIGAPOR,
                    RETROMESANT,
                    OTRORETRO,
                    SALARIOSINAPO,
                    APORJUB
                FROM sueldo0425 a
                JOIN imponi0425B b ON a.PADROND = b.PADROND
                WHERE a.CENTRO = 27
                ORDER BY a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reporte_reparto_poder_judicial'
    ],

    // Categoría: Con Ajuste
    'imponible_hidraulica' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'DEPARTAMENTO HIDRAULICA - IMPONIBLE',
        'descripcion' => 'Consulta de datos de imponible hidráulica para el centro 22. Incluye información sobre empleados, salarios y aportes.',
        'archivo' => 'scripts_sql/hidraulica.sql',
        'tipo' => 'sql',
        'permiso' => 'ver_reporte_hidraulica'
    ],
    'imponible_hidraulica_con_ajuste' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Imponible Hidráulica - Con Ajuste',
        'descripcion' => 'Consulta de datos de imponible hidráulica con ajustes aplicados. Incluye información sobre empleados, salarios y aportes.',
        'archivo' => 'scripts_sql/hidraulica.sql',
        'tipo' => 'sql',
        'permiso' => 'ver_reporte_hidraulica'
    ],
    'imponible_policia' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Imponible Policía',
        'descripcion' => 'Consulta de datos de imponible para el personal de policía. Incluye información sobre empleados, salarios y aportes.',
        'archivo' => 'scripts_sql/imponible_policia.sql',
        'tipo' => 'sql',
        'permiso' => 'ver_consulta_policia'
    ],
    'resumen_centros' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Resumen por Centros',
        'descripcion' => 'Resumen de imponibles agrupados por centro. Muestra totales y promedios.',
        'archivo' => 'scripts_sql/resumen_centros.sql',
        'tipo' => 'sql',
        'permiso' => 'ver_reporte_resumen_centros'
    ],
    'resumen_sectores' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Resumen por Sectores',
        'descripcion' => 'Resumen de imponibles agrupados por sector. Muestra totales y promedios.',
        'archivo' => 'scripts_sql/resumen_sectores.sql',
        'tipo' => 'sql',
        'permiso' => 'ver_reporte_resumen_sectores'
    ],


    // Reportes adicionales de consultas.php en categoría "Con Ajuste"
    'imponible_hidraulica_original' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Imponible Hidráulica - Centro 22 (Original)',
        'descripcion' => 'Consulta de datos de imponible hidráulica para el centro 22 (versión original).',
        'sql' => "",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reporte_hidraulica'
    ],
    'imponible_policia_original' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Imponible Policía (Original)',
        'descripcion' => 'Consulta de datos de imponible para el personal de policía (versión original).',
        'sql' => "SELECT
                    a.CENTRO,
                    a.SECTOR,
                    a.PADROND,
                    a.APYNOM,
                    a.CUIL,
                    a.ANTIG,
                    a.PERM_CAT,
                    a.CATEG,
                    a.ESCALAFON,
                    a.FNACIM,
                    ASIGAPOR,
                    RETROMESANT,
                    OTRORETRO,
                    OTROCONCEPSINAPO,
                    SALARIOSINAPO,
                    APORJUB
                FROM sueldo0425 a
                JOIN imponi0425B b ON a.PADROND = b.PADROND
                WHERE a.CENTRO = 11
                ORDER BY a.CENTRO, a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_consulta_policia'
    ],
    'resumen_centros_original' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Resumen por Centros (Original)',
        'descripcion' => 'Resumen de imponibles agrupados por centro (versión original).',
        'sql' => "SELECT
                    a.CENTRO,
                    COUNT(a.PADROND) as CANTIDAD,
                    SUM(ASIGAPOR) as TOTAL_ASIGAPOR,
                    AVG(ASIGAPOR) as PROMEDIO_ASIGAPOR,
                    SUM(APORJUB) as TOTAL_APORJUB,
                    AVG(APORJUB) as PROMEDIO_APORJUB
                FROM sueldo0425 a
                JOIN imponi0425B b ON a.PADROND = b.PADROND
                GROUP BY a.CENTRO
                ORDER BY a.CENTRO",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reporte_resumen_centros'
    ],
    'obra_social_provincia_completo' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Obra Social Provincia (Completo)',
        'descripcion' => 'Consulta detallada de aportes a la Obra Social de la Provincia con todos los conceptos relacionados. Incluye datos de CIMENS y CIESCU.',
        'sql' => "
SELECT
    ANOLIQ as \"AÑO\",
    MESLIQ as \"MES\",
    NUM_DOC as \"DNI\",
    CUIL as \"CUIL\",
    APYNOM as \"NOMBRE\",
    CENTRO as \"CENTRO\",
    SECTOR as \"SECTOR\",
    PADRON as \"PADRON\",
    REGIMEN as \"REGIMEN\",
    TACAREDU as \"IMPONIBLE\",
    sum(H02) AS \"AP_PER_IMP\",
    APPER_OBSOC as \"APPER_OBSOC\",
    SUM(H08) as \"AP_COL_IMP\",
    (ap_colat/10) as \"AP_COLAT_DIV\",
    sum(I62) as \"I62\",
    sum(K20) as \"K20\",
    sum(H58) as \"H58_IMPO\",
    APPER_OBSOC AS \"H58_PORC\",
    sum(H59) as \"H59_IMPO\",
    ap_colat as \"H59_PORC\",
    APPAT_OBSOC as \"APPATRON\",
    sum(F10) as \"F10\",
    sum(F11) as \"F11\",
    SUM(K63) as \"K63\",
    sum(H04) as \"H04\",
    sum(H34) as \"H34\",
    sum(H40) as \"H40\"
FROM (
    SELECT A.ANOLIQ, A.MESLIQ, A.CENTRO, A.SECTOR, A.PADROND as padron, A.APYNOM, A.REGIMEN, A.NUM_DOC, A.CUIL, a.tacaredu, A.APPER_OBSOC, a.APPAT_OBSOC, ap_colat, b.*
    FROM exp5.sueldo0425 A
    JOIN (
        SELECT * FROM (SELECT padrond, codigo, importe FROM EXP5.CIMENS0425)
        PIVOT (
            sum(importe)
            FOR codigo IN (
                'H02' H02,
                'H08' H08,
                'I62' I62,
                'K20' K20,
                'H58' H58,
                'H59' H59,
                'I07' I07,
                'K10' K10,
                'K26' K26,
                'K63' K63,
                'F10' F10,
                'F11' F11,
                'H04' H04,
                'H40' H40,
                'H34' H34
            )
        )
    ) B ON A.PADROND = b.padrond
    UNION
    SELECT A.ANOLIQ, A.MESLIQ, A.CENTRO, A.SECTOR, A.PADROND, A.APYNOM, A.REGIMEN, A.NUM_DOC, A.CUIL, a.tacaredu, A.APPER_OBSOC, a.APPAT_OBSOC, ap_colat, b.*
    FROM exp5.sueldo0425 A
    JOIN (
        SELECT * FROM (SELECT padrond, codigo, importe FROM EXP5.CIescu0425)
        PIVOT (
            sum(importe)
            FOR codigo IN (
                'H02' H02,
                'H08' H08,
                'I62' I62,
                'K20' K20,
                'H58' H58,
                'H59' H59,
                'I07' I07,
                'K10' K10,
                'K26' K26,
                'K63' K63,
                'F10' F10,
                'F11' F11,
                'H04' H04,
                'H40' H40,
                'H34' H34
            )
        )
    ) B ON A.PADROND = b.padrond
)
GROUP BY padron, ANOLIQ, MESLIQ, CENTRO, SECTOR, APYNOM, REGIMEN, NUM_DOC, CUIL, TACAREDU, APPER_OBSOC, APPAT_OBSOC, ap_colat, H02, H08, I62, K20, H58, H59, I07, K10, K26, K63, F10, F11, H04, H40, H34
",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reporte_obra_social'
    ],

    // Nuevos reportes en categoría "Con Ajuste"
    'imponible_por_categoria' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Imponible por Categoría',
        'descripcion' => 'Reporte que muestra el imponible agrupado por categoría de empleados. Incluye totales y promedios.',
        'archivo' => 'scripts_sql/imponible_por_categoria.sql',
        'tipo' => 'sql',
        'permiso' => 'ver_reportes'
    ],
    'aportes_jubilatorios_por_centro' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Aportes Jubilatorios por Centro',
        'descripcion' => 'Reporte que muestra los aportes jubilatorios agrupados por centro. Incluye totales, promedios y porcentajes.',
        'archivo' => 'scripts_sql/aportes_jubilatorios_por_centro.sql',
        'tipo' => 'sql',
        'permiso' => 'ver_reportes'
    ],
    'empleados_antiguedad_mayor_20' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Empleados con Antigüedad > 20 años',
        'descripcion' => 'Reporte que muestra los empleados que tienen más de 20 años de antigüedad, ordenados por antigüedad descendente.',
        'archivo' => 'scripts_sql/empleados_antiguedad_mayor_20.sql',
        'tipo' => 'sql',
        'permiso' => 'ver_reportes'
    ],
    'obra_social_provincia_regimen_1_21' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Obra Social Provincia Regimen 1 y 21',
        'descripcion' => 'Consulta de empleados con regimen 001 y 021 que tienen TACAREDU = 0. Muestra información básica del empleado.',
        'sql' => "SELECT
    centro as \"CENTRO\",
    sector as \"SECTOR\",
    padrond as \"PADRON\",
    Apynom as \"NOMBRE\",
    num_doc as \"DNI\",
    CUIL as \"CUIL\",
    regimen as \"REGIMEN\"
FROM sueldo0425
WHERE regimen in(001,021) and TACAREDU=0
ORDER BY CENTRO,SECTOR,APYNOM",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reportes'
    ],
    'direccion_general_rentas_listado_mensual' => [
        'categoria' => 'con_ajuste',
        'titulo' => 'Direccion General de Rentas Listado Mensual',
        'descripcion' => 'Listado mensual de empleados de la Dirección General de Rentas. Incluye información del centro, sector, ministerio y datos del empleado.',
        'sql' => "SELECT
    A.CENTRO as \"CENTRO\",
    B.NOMCENTRO as \"NOMBRE_CENTRO\",
    A.SECTOR as \"SECTOR\",
    B.NOMSECTOR as \"NOMBRE_SECTOR\",
    B.NOMMINISTERIO as \"NOMBRE_MINISTERIO\",
    A.PADROND as \"PADRON\",
    A.CUIL as \"CUIL\",
    A.APYNOM as \"NOMBRE\",
    A.CLASE as \"CLASE\",
    A.ESCALAFON as \"ESCALAFON\",
    A.CATEG as \"CATEGORIA\"
FROM sueldo0425 A, REPARTICIONES B
WHERE A.CENTRO=B.CENTRO AND A.SECTOR=B.SECTOR
ORDER BY A.CENTRO, A.SECTOR, A.APYNOM",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reportes'
    ],

    // Nuevos reportes para la pestaña "Sin Ajuste"
    'legislatura_imponible' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'LEGISLATURA - IMPONIBLE',
        'descripcion' => 'Consulta de imponible para la Legislatura Provincial.',
        'sql' => "SELECT
            a.CENTRO as \"CENTRO\",
            a.SECTOR as \"SECTOR\",
            a.PADROND as \"PADRON\",
            a.APYNOM as \"NOMBRE\",
            a.CUIL as \"CUIL\",
            a.CATEG as \"CATEGORIA\",
            a.ESCALAFON as \"ESCALAFON\",
            b.ASIGAPOR as \"ASIGAPOR\",
            b.RETROMESANT as \"RETROMESANT\",
            b.OTRORETRO as \"OTRORETRO\",
            b.SALARIOSINAPO as \"SALARIOSINAPO\",
            b.APORJUB as \"APORJUB\"
        FROM sueldo0425 a
        JOIN imponi0425B b ON a.PADROND = b.PADROND
        WHERE a.CENTRO = 28
        ORDER BY a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reportes'
    ],
    'legislatura_reparto' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'LEGISLATURA - REPARTO',
        'descripcion' => 'Consulta de reparto para la Legislatura Provincial.',
        'sql' => "SELECT
            a.CENTRO as \"CENTRO\",
            a.SECTOR as \"SECTOR\",
            a.PADROND as \"PADRON\",
            a.APYNOM as \"NOMBRE\",
            a.CUIL as \"CUIL\",
            a.CATEG as \"CATEGORIA\",
            a.ESCALAFON as \"ESCALAFON\",
            a.TACAREDU as \"TACAREDU\",
            a.TASAREDU as \"TASAREDU\",
            a.TOTDESC as \"TOTDESC\",
            a.NETO as \"NETO\"
        FROM sueldo0425 a
        WHERE a.CENTRO = 28
        ORDER BY a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reportes'
    ],
    'policia_imponible' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'POLICIA - IMPONIBLE',
        'descripcion' => 'Consulta de imponible para la Policía Provincial.',
        'sql' => "SELECT
            a.CENTRO as \"CENTRO\",
            a.SECTOR as \"SECTOR\",
            a.PADROND as \"PADRON\",
            a.APYNOM as \"NOMBRE\",
            a.CUIL as \"CUIL\",
            a.ANTIG as \"ANTIGUEDAD\",
            a.PERM_CAT as \"PERM_CAT\",
            a.CATEG as \"CATEGORIA\",
            a.ESCALAFON as \"ESCALAFON\",
            a.FNACIM as \"FNACIM\",
            b.ASIGAPOR as \"ASIGAPOR\",
            b.RETROMESANT as \"RETROMESANT\",
            b.OTRORETRO as \"OTRORETRO\",
            b.OTROCONCEPSINAPO as \"OTROCONCEPSINAPO\",
            b.SALARIOSINAPO as \"SALARIOSINAPO\",
            b.APORJUB as \"APORJUB\"
        FROM sueldo0425 a
        JOIN imponi0425B b ON a.PADROND = b.PADROND
        WHERE a.CENTRO = 11
        ORDER BY a.CENTRO, a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reportes'
    ],
    'nivel_central_imponible' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'NIVEL CENTRAL - IMPONIBLE',
        'descripcion' => 'Consulta de imponible para el Nivel Central.',
        'sql' => "SELECT
            a.CENTRO as \"CENTRO\",
            a.SECTOR as \"SECTOR\",
            a.PADROND as \"PADRON\",
            a.APYNOM as \"NOMBRE\",
            a.CUIL as \"CUIL\",
            a.CATEG as \"CATEGORIA\",
            a.ESCALAFON as \"ESCALAFON\",
            b.ASIGAPOR as \"ASIGAPOR\",
            b.RETROMESANT as \"RETROMESANT\",
            b.OTRORETRO as \"OTRORETRO\",
            b.SALARIOSINAPO as \"SALARIOSINAPO\",
            b.APORJUB as \"APORJUB\"
        FROM sueldo0425 a
        JOIN imponi0425B b ON a.PADROND = b.PADROND
        WHERE a.CENTRO = 01
        ORDER BY a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reportes'
    ],
    'hospital_rawson_imponible' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'HOSPITAL RAWSON - IMPONIBLE',
        'descripcion' => 'Consulta de imponible para el Hospital Rawson.',
        'sql' => "SELECT
            a.CENTRO as \"CENTRO\",
            a.SECTOR as \"SECTOR\",
            a.PADROND as \"PADRON\",
            a.APYNOM as \"NOMBRE\",
            a.CUIL as \"CUIL\",
            a.CATEG as \"CATEGORIA\",
            a.ESCALAFON as \"ESCALAFON\",
            b.ASIGAPOR as \"ASIGAPOR\",
            b.RETROMESANT as \"RETROMESANT\",
            b.OTRORETRO as \"OTRORETRO\",
            b.SALARIOSINAPO as \"SALARIOSINAPO\",
            b.APORJUB as \"APORJUB\"
        FROM sueldo0425 a
        JOIN imponi0425B b ON a.PADROND = b.PADROND
        WHERE a.CENTRO = 12
        ORDER BY a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reportes'
    ],
    'hospital_marcial_quiroga_imponible' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'HOSPITAL MARCIAL QUIROGA - IMPONIBLE',
        'descripcion' => 'Consulta de imponible para el Hospital Marcial Quiroga.',
        'sql' => "SELECT
            a.CENTRO as \"CENTRO\",
            a.SECTOR as \"SECTOR\",
            a.PADROND as \"PADRON\",
            a.APYNOM as \"NOMBRE\",
            a.CUIL as \"CUIL\",
            a.CATEG as \"CATEGORIA\",
            a.ESCALAFON as \"ESCALAFON\",
            b.ASIGAPOR as \"ASIGAPOR\",
            b.RETROMESANT as \"RETROMESANT\",
            b.OTRORETRO as \"OTRORETRO\",
            b.SALARIOSINAPO as \"SALARIOSINAPO\",
            b.APORJUB as \"APORJUB\"
        FROM sueldo0425 a
        JOIN imponi0425B b ON a.PADROND = b.PADROND
        WHERE a.CENTRO = 13
        ORDER BY a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reportes'
    ],
    'servicio_penitenciario_imponible_con_recargo' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'SERVICIO PENITENCIARIO - IMPONIBLE CON RECARGO',
        'descripcion' => 'Consulta de imponible con recargo para el Servicio Penitenciario.',
        'sql' => "SELECT
            a.CENTRO as \"CENTRO\",
            a.SECTOR as \"SECTOR\",
            a.PADROND as \"PADRON\",
            a.APYNOM as \"NOMBRE\",
            a.CUIL as \"CUIL\",
            a.CATEG as \"CATEGORIA\",
            a.ESCALAFON as \"ESCALAFON\",
            b.ASIGAPOR as \"ASIGAPOR\",
            b.RETROMESANT as \"RETROMESANT\",
            b.OTRORETRO as \"OTRORETRO\",
            b.SALARIOSINAPO as \"SALARIOSINAPO\",
            b.APORJUB as \"APORJUB\"
        FROM sueldo0425 a
        JOIN imponi0425B b ON a.PADROND = b.PADROND
        WHERE a.CENTRO = 14 AND a.RECARGO > 0
        ORDER BY a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reportes'
    ],
    'servicio_penitenciario_imponible_sin_recargo' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'SERVICIO PENITENCIARIO - IMPONIBLE SIN RECARGO',
        'descripcion' => 'Consulta de imponible sin recargo para el Servicio Penitenciario.',
        'sql' => "SELECT
            a.CENTRO as \"CENTRO\",
            a.SECTOR as \"SECTOR\",
            a.PADROND as \"PADRON\",
            a.APYNOM as \"NOMBRE\",
            a.CUIL as \"CUIL\",
            a.CATEG as \"CATEGORIA\",
            a.ESCALAFON as \"ESCALAFON\",
            b.ASIGAPOR as \"ASIGAPOR\",
            b.RETROMESANT as \"RETROMESANT\",
            b.OTRORETRO as \"OTRORETRO\",
            b.SALARIOSINAPO as \"SALARIOSINAPO\",
            b.APORJUB as \"APORJUB\"
        FROM sueldo0425 a
        JOIN imponi0425B b ON a.PADROND = b.PADROND
        WHERE a.CENTRO = 14 AND (a.RECARGO = 0 OR a.RECARGO IS NULL)
        ORDER BY a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reportes'
    ],
    'servicio_penitenciario_servicio_con_recargo' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'SERVICIO PENITENCIARIO - SERVICIO CON RECARGO',
        'descripcion' => 'Consulta de servicio con recargo para el Servicio Penitenciario.',
        'sql' => "SELECT
            a.CENTRO as \"CENTRO\",
            a.SECTOR as \"SECTOR\",
            a.PADROND as \"PADRON\",
            a.APYNOM as \"NOMBRE\",
            a.CUIL as \"CUIL\",
            a.CATEG as \"CATEGORIA\",
            a.ESCALAFON as \"ESCALAFON\",
            a.TACAREDU as \"TACAREDU\",
            a.TASAREDU as \"TASAREDU\",
            a.TOTDESC as \"TOTDESC\",
            a.NETO as \"NETO\",
            a.RECARGO as \"RECARGO\"
        FROM sueldo0425 a
        WHERE a.CENTRO = 14 AND a.RECARGO > 0
        ORDER BY a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reportes'
    ],
    'servicio_penitenciario_servicio_sin_recargo' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'SERVICIO PENITENCIARIO - SERVICIO SIN RECARGO',
        'descripcion' => 'Consulta de servicio sin recargo para el Servicio Penitenciario.',
        'sql' => "SELECT
            a.CENTRO as \"CENTRO\",
            a.SECTOR as \"SECTOR\",
            a.PADROND as \"PADRON\",
            a.APYNOM as \"NOMBRE\",
            a.CUIL as \"CUIL\",
            a.CATEG as \"CATEGORIA\",
            a.ESCALAFON as \"ESCALAFON\",
            a.TACAREDU as \"TACAREDU\",
            a.TASAREDU as \"TASAREDU\",
            a.TOTDESC as \"TOTDESC\",
            a.NETO as \"NETO\"
        FROM sueldo0425 a
        WHERE a.CENTRO = 14 AND (a.RECARGO = 0 OR a.RECARGO IS NULL)
        ORDER BY a.SECTOR, a.PADROND",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reportes'
    ],
    'municipalidad_chimbas_mensual' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'MUNICIPALIDAD DE CHIMBAS - MENSUAL',
        'descripcion' => 'Consulta mensual para la Municipalidad de Chimbas. Incluye información detallada de empleados y conceptos.',
        'sql' => "SELECT
            a.CENTRO as \"CENTRO\",
            a.SECTOR as \"SECTOR\",
            a.PADROND as \"PADRON\",
            a.APYNOM as \"NOMBRE\",
            a.CUIL as \"CUIL\",
            a.PLANTA as \"PLANTA\",
            a.ESCALAFON as \"ESCALAFON\",
            a.CATEG as \"CATEGORIA\",
            a.ANTIG as \"ANTIGUEDAD\",
            a.TACAREDU as \"TACAREDU\",
            a.TASAREDU as \"TASAREDU\",
            a.TOTDESC as \"TOTDESC\",
            a.NETO as \"NETO\",
            b.CODIGO as \"CODIGO\",
            b.IMPORTE as \"IMPORTE\"
        FROM sueldo0425 a, ciescu0425 b
        WHERE a.PADROND = b.PADROND AND a.CENTRO = 65
        ORDER BY a.PADROND, b.CODIGO",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reportes'
    ],
    'municipalidad_calingasta_mensual' => [
        'categoria' => 'sin_ajuste',
        'titulo' => 'MUNICIPALIDAD DE CALINGASTA - MENSUAL',
        'descripcion' => 'Consulta mensual para la Municipalidad de Calingasta. Incluye información detallada de empleados y conceptos.',
        'sql' => "SELECT
            a.CENTRO as \"CENTRO\",
            a.SECTOR as \"SECTOR\",
            a.PADROND as \"PADRON\",
            a.APYNOM as \"NOMBRE\",
            a.CUIL as \"CUIL\",
            a.PLANTA as \"PLANTA\",
            a.ESCALAFON as \"ESCALAFON\",
            a.CATEG as \"CATEGORIA\",
            a.ANTIG as \"ANTIGUEDAD\",
            a.TACAREDU as \"TACAREDU\",
            a.TASAREDU as \"TASAREDU\",
            a.TOTDESC as \"TOTDESC\",
            a.NETO as \"NETO\",
            b.CODIGO as \"CODIGO\",
            b.IMPORTE as \"IMPORTE\"
        FROM sueldo0425 a, ciescu0425 b
        WHERE a.PADROND = b.PADROND AND a.CENTRO = 66
        ORDER BY a.PADROND, b.CODIGO",
        'tipo' => 'sql_directo',
        'permiso' => 'ver_reportes'
    ]
];

/**
 * Obtiene las categorías de consultas
 * @return array Categorías de consultas
 */
function getCategorias() {
    global $categorias;

    // Depuración
    error_log("Obteniendo categorías. Total: " . count($categorias));
    foreach ($categorias as $key => $cat) {
        error_log("Categoría: " . $key . " - Título: " . (isset($cat['titulo']) ? $cat['titulo'] : 'No definido'));
    }

    $result = [];
    foreach ($categorias as $key => $cat) {
        if (isset($cat['titulo'])) {
            $result[] = $cat['titulo'];
            error_log("Agregada categoría a resultado: " . $cat['titulo']);
        }
    }

    error_log("Total de categorías en resultado: " . count($result));
    return $result;
}

/**
 * Obtiene las consultas de una categoría específica
 * @param string $categoria Título de la categoría
 * @return array Consultas de la categoría
 */
function getConsultasPorCategoria($categoria) {
    global $consultas, $categorias;

    // Depuración
    error_log("Buscando consultas para la categoría: " . $categoria);

    // Encontrar la clave de la categoría por su título
    $categoriaKey = false;
    foreach ($categorias as $key => $cat) {
        if (isset($cat['titulo']) && $cat['titulo'] === $categoria) {
            $categoriaKey = $key;
            error_log("Encontrada clave de categoría: " . $key);
            break;
        }
    }

    if ($categoriaKey === false) {
        error_log("No se encontró la clave para la categoría: " . $categoria);
        return [];
    }

    // Filtrar consultas por categoría
    $resultado = [];
    foreach ($consultas as $key => $consulta) {
        if (isset($consulta['categoria']) && $consulta['categoria'] === $categoriaKey) {
            $resultado[$key] = $consulta;
            error_log("Agregada consulta a resultado: " . $key);
        }
    }

    error_log("Total de consultas encontradas para categoría " . $categoria . ": " . count($resultado));
    return $resultado;
}
?>
