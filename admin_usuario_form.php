<?php
// Incluir archivo de permisos
require_once 'permisos.php';

// Verificar que el usuario esté autenticado y tenga permiso
requireLogin();
requirePermiso('admin_usuarios');

$mensaje = '';
$error = '';
$usuario = [
    'id' => '',
    'username' => '',
    'nombre' => '',
    'email' => '',
    'rol' => 'usuario',
    'activo' => 1
];

// Verificar si es edición o creación
$isEditing = false;
if (isset($_GET['id']) && is_numeric($_GET['id'])) {
    $isEditing = true;
    $id = $_GET['id'];
    $usuarioData = getUserById($id);

    if ($usuarioData) {
        $usuario = $usuarioData;
    } else {
        $error = 'Usuario no encontrado';
    }
}

// Procesar el formulario
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Recoger datos del formulario
    $userData = [
        'username' => $_POST['username'] ?? '',
        'password' => $_POST['password'] ?? '',
        'nombre' => $_POST['nombre'] ?? '',
        'email' => $_POST['email'] ?? '',
        'rol' => $_POST['rol'] ?? 'usuario',
        'activo' => isset($_POST['activo']) ? 1 : 0
    ];

    // Validar datos
    if (empty($userData['username']) || empty($userData['nombre']) || empty($userData['email'])) {
        $error = 'Todos los campos son obligatorios excepto la contraseña al editar';
    } else {
        if ($isEditing) {
            // Actualizar usuario existente
            $userData['id'] = $id;
            $result = updateUser($userData);

            if ($result === true) {
                $mensaje = 'Usuario actualizado correctamente';
                // Actualizar datos mostrados
                $usuario = getUserById($id);
            } else {
                $error = $result;
            }
        } else {
            // Crear nuevo usuario
            if (empty($userData['password'])) {
                $error = 'La contraseña es obligatoria para nuevos usuarios';
            } else {
                $result = registerUser($userData);

                if ($result === true) {
                    $mensaje = 'Usuario creado correctamente';
                    // Limpiar formulario
                    $usuario = [
                        'id' => '',
                        'username' => '',
                        'nombre' => '',
                        'email' => '',
                        'rol' => 'usuario',
                        'activo' => 1
                    ];
                } else {
                    $error = $result;
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $isEditing ? 'Editar' : 'Nuevo'; ?> Usuario - Sistema de Consultas Oracle</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .form-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input[type="text"],
        .form-group input[type="email"],
        .form-group input[type="password"],
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 16px;
        }

        .form-group .checkbox-label {
            display: inline-flex;
            align-items: center;
            font-weight: normal;
        }

        .form-group .checkbox-label input {
            margin-right: 10px;
        }

        .form-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>Sistema de Consultas Oracle</h1>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul>
                <li><a href="index.php">Inicio</a></li>
                <?php
                // Verificar si el usuario tiene permiso para ver al menos una consulta
                $mostrarReportes = tienePermiso('ver_reportes');
                if (!$mostrarReportes) {
                    // Verificar permisos específicos para consultas
                    if (tienePermiso('ver_consulta_hidraulica') || tienePermiso('ver_consulta_policia')) {
                        $mostrarReportes = true;
                    }
                }
                if ($mostrarReportes):
                ?>
                    <li><a href="reportes.php">Reportes</a></li>
                <?php endif; ?>
                <?php if (tienePermiso('admin_usuarios')): ?>
                    <li><a href="admin_usuarios.php">Administrar Usuarios</a></li>
                    <li><a href="admin_permisos_usuario.php">Administrar Permisos</a></li>
                <?php endif; ?>
                <li><a href="logout.php">Cerrar Sesión (<?php echo htmlspecialchars($_SESSION['user_username']); ?>)</a></li>
            </ul>
        </div>
    </nav>

    <main class="container">
        <h2><?php echo $isEditing ? 'Editar' : 'Nuevo'; ?> Usuario</h2>

        <?php if (!empty($mensaje)): ?>
            <div class="success"><?php echo $mensaje; ?></div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>

        <div class="form-container">
            <form method="post">
                <div class="form-group">
                    <label for="username">Usuario</label>
                    <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($usuario['username']); ?>" required>
                </div>

                <div class="form-group">
                    <label for="password">Contraseña <?php echo $isEditing ? '(dejar en blanco para mantener la actual)' : ''; ?></label>
                    <input type="password" id="password" name="password" <?php echo $isEditing ? '' : 'required'; ?>>
                </div>

                <div class="form-group">
                    <label for="nombre">Nombre Completo</label>
                    <input type="text" id="nombre" name="nombre" value="<?php echo htmlspecialchars($usuario['nombre']); ?>" required>
                </div>

                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($usuario['email']); ?>" required>
                </div>

                <div class="form-group">
                    <label for="rol">Rol</label>
                    <select id="rol" name="rol">
                        <option value="usuario" <?php echo $usuario['rol'] === 'usuario' ? 'selected' : ''; ?>>Usuario</option>
                        <option value="admin" <?php echo $usuario['rol'] === 'admin' ? 'selected' : ''; ?>>Administrador</option>
                    </select>
                </div>

                <?php if ($isEditing): ?>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="activo" <?php echo $usuario['activo'] ? 'checked' : ''; ?>>
                        Usuario activo
                    </label>
                </div>
                <?php endif; ?>

                <div class="form-buttons">
                    <a href="admin_usuarios.php" class="btn">Cancelar</a>
                    <button type="submit" class="btn btn-success"><?php echo $isEditing ? 'Actualizar' : 'Crear'; ?> Usuario</button>
                </div>
            </form>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Sistema de Consultas Oracle. Todos los derechos reservados.</p>
        </div>
    </footer>
</body>
</html>
