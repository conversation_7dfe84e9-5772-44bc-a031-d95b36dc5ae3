<?php
// Iniciar sesión
session_start();

// Guardar el nombre de usuario para mostrar un mensaje
$username = isset($_SESSION['user_username']) ? $_SESSION['user_username'] : 'desconocido';

// Limpiar la caché de permisos
if (isset($_SESSION['permisos_cache'])) {
    unset($_SESSION['permisos_cache']);
}

// Destruir la sesión
session_destroy();

// Iniciar una nueva sesión
session_start();

// Mostrar mensaje
$_SESSION['mensaje'] = "La sesión del usuario $username ha sido limpiada correctamente y la caché de permisos ha sido eliminada. Por favor, inicia sesión nuevamente.";

// Redirigir a la página de inicio de sesión
header('Location: login.php');
exit;
?>
