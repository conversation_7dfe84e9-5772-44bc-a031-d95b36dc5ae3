<?php
/**
 * Ejecuta un script SQL y devuelve los resultados
 * 
 * Este archivo se encarga de ejecutar un script SQL y devolver los resultados
 * para mostrarlos en la página de reportes.
 */

// Incluir archivos necesarios
require_once 'conexion.php';
require_once 'permisos.php';

/**
 * Ejecuta un script SQL y devuelve los resultados
 * @param string $scriptPath Ruta al archivo SQL
 * @return array Resultados de la consulta
 */
function ejecutarScriptSQL($scriptPath) {
    // Verificar que el archivo exista
    if (!file_exists($scriptPath)) {
        throw new Exception("El archivo de script SQL no existe: $scriptPath");
    }
    
    // Leer el contenido del archivo
    $scriptContent = file_get_contents($scriptPath);
    
    // Verificar que el contenido no esté vacío
    if (empty($scriptContent)) {
        throw new Exception("El archivo de script SQL está vacío: $scriptPath");
    }
    
    // Separar el script en sentencias individuales
    $statements = [];
    
    // Verificar si el script contiene la marca "-- Consulta final"
    if (strpos($scriptContent, '-- Consulta final') !== false) {
        // Separar el script SQL de la consulta final
        $parts = explode('-- Consulta final', $scriptContent);
        
        if (count($parts) == 2) {
            $sqlScript = trim($parts[0]);
            $querySQL = trim($parts[1]);
            
            // Dividir el script en sentencias individuales
            $statements = preg_split('/;[\s]*[\r\n]+/', $sqlScript);
            
            // Agregar la consulta final como la última sentencia
            $statements[] = $querySQL;
        } else {
            // Si no se puede separar, tratar todo como una sola sentencia
            $statements[] = $scriptContent;
        }
    } else {
        // Si no tiene la marca de consulta final, dividir por punto y coma
        $statements = preg_split('/;[\s]*[\r\n]+/', $scriptContent);
    }
    
    // Conectar a la base de datos
    $pdo = getConexion();
    
    // Ejecutar cada sentencia individualmente
    $lastStatement = null;
    $lastResult = null;
    
    foreach ($statements as $index => $statement) {
        $statement = trim($statement);
        
        // Ignorar sentencias vacías
        if (empty($statement)) {
            continue;
        }
        
        // Ignorar comentarios
        if (strpos($statement, '--') === 0) {
            continue;
        }
        
        // Verificar si es la última sentencia (consulta final)
        $isLastStatement = ($index == count($statements) - 1);
        
        try {
            // Si es la última sentencia, ejecutarla como una consulta y guardar el resultado
            if ($isLastStatement) {
                $lastStatement = $statement;
                $stmt = $pdo->query($statement);
                $lastResult = $stmt;
            } else {
                // Para las demás sentencias, ejecutarlas sin guardar el resultado
                $pdo->exec($statement);
            }
        } catch (PDOException $e) {
            // Si es un error de "tabla no existe" al hacer DROP, ignorarlo
            if (strpos($statement, 'DROP TABLE') !== false && strpos($e->getMessage(), 'ORA-00942') !== false) {
                // Ignorar este error específico
            } else {
                // Para otros errores, lanzar una excepción
                throw new Exception("Error al ejecutar la sentencia SQL: " . $e->getMessage() . "\nSentencia: " . $statement);
            }
        }
    }
    
    // Verificar que se haya ejecutado al menos una sentencia
    if ($lastResult === null) {
        throw new Exception("No se encontró una consulta final en el script SQL: $scriptPath");
    }
    
    return $lastResult;
}
?>
