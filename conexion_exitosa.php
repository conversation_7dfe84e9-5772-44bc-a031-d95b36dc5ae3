<?php
// Establecer el modo de visualización de errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir el archivo de conexión
require_once 'CONEXION.PHP';

// Función para mostrar un mensaje de éxito
function mostrarExito($mensaje) {
    echo "<div style='padding: 20px; background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; border-radius: 4px; margin: 20px 0; text-align: center; font-size: 18px;'>";
    echo "<strong>✓ ÉXITO:</strong> $mensaje";
    echo "</div>";
}

// Función para mostrar un mensaje de error
function mostrarError($mensaje) {
    echo "<div style='padding: 20px; background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 4px; margin: 20px 0; text-align: center; font-size: 18px;'>";
    echo "<strong>✗ ERROR:</strong> $mensaje";
    echo "</div>";
}

// Función para mostrar información
function mostrarInfo($titulo, $contenido) {
    echo "<div style='padding: 15px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; margin: 15px 0;'>";
    echo "<h3 style='margin-top: 0; color: #333;'>$titulo</h3>";
    echo $contenido;
    echo "</div>";
}

// Estilo de la página
echo "<!DOCTYPE html>
<html lang='es'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Conexión Exitosa a Oracle</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: auto;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>Prueba de Conexión a Oracle</h1>";

try {
    // Intentar obtener una conexión
    $pdo = getConexion();
    
    // Si llegamos aquí, la conexión fue exitosa
    mostrarExito("La conexión a la base de datos Oracle se ha establecido correctamente.");
    
    // Ejecutar una consulta simple para verificar la conexión
    $stmt = $pdo->query("SELECT 'La conexión funciona correctamente' AS MENSAJE FROM DUAL");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    mostrarInfo("Resultado de la consulta", "<p>" . $result['MENSAJE'] . "</p>");
    
    // Mostrar información de la conexión
    $serverInfo = "<ul>
        <li><strong>Versión del servidor:</strong> " . $pdo->getAttribute(PDO::ATTR_SERVER_VERSION) . "</li>
        <li><strong>Versión del cliente:</strong> " . $pdo->getAttribute(PDO::ATTR_CLIENT_VERSION) . "</li>
        <li><strong>Estado de la conexión:</strong> " . $pdo->getAttribute(PDO::ATTR_CONNECTION_STATUS) . "</li>
    </ul>";
    
    mostrarInfo("Información de la conexión", $serverInfo);
    
    // Mostrar tablas disponibles
    try {
        $stmt = $pdo->query("SELECT TABLE_NAME FROM USER_TABLES WHERE ROWNUM <= 10");
        $tables = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $tablesList = "<ul>";
        foreach ($tables as $table) {
            $tablesList .= "<li>" . $table['TABLE_NAME'] . "</li>";
        }
        $tablesList .= "</ul>";
        
        mostrarInfo("Tablas disponibles (primeras 10)", $tablesList);
    } catch (PDOException $e) {
        mostrarInfo("Tablas disponibles", "<p>No se pudieron obtener las tablas: " . $e->getMessage() . "</p>");
    }
    
    // Mostrar enlaces a otras páginas
    echo "<div style='margin-top: 30px; text-align: center;'>";
    echo "<a href='index.php' class='btn'>Ir a la página principal</a>";
    echo "<a href='reportes.php' class='btn'>Ver reportes</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    // Si hay un error, mostrarlo
    mostrarError("No se pudo establecer la conexión a la base de datos: " . $e->getMessage());
    
    // Mostrar sugerencias para solucionar el problema
    $sugerencias = "<ol>
        <li>Verifica que el servidor Oracle esté en ejecución.</li>
        <li>Comprueba que las credenciales de usuario y contraseña sean correctas.</li>
        <li>Asegúrate de que el nombre del servicio o SID sea correcto.</li>
        <li>Verifica que el firewall no esté bloqueando la conexión.</li>
        <li>Comprueba que las extensiones OCI8 y PDO_OCI estén instaladas y habilitadas en PHP.</li>
    </ol>";
    
    mostrarInfo("Sugerencias para solucionar el problema", $sugerencias);
    
    // Mostrar información de PHP
    $phpInfo = "<ul>
        <li><strong>Versión de PHP:</strong> " . phpversion() . "</li>
        <li><strong>Extensión OCI8:</strong> " . (extension_loaded('oci8') ? 'Cargada' : 'No cargada') . "</li>
        <li><strong>Extensión PDO_OCI:</strong> " . (extension_loaded('pdo_oci') ? 'Cargada' : 'No cargada') . "</li>
    </ul>";
    
    mostrarInfo("Información de PHP", $phpInfo);
    
    // Mostrar configuración de conexión
    $conexionInfo = "<pre>" . htmlspecialchars(file_get_contents('CONEXION.PHP')) . "</pre>";
    mostrarInfo("Configuración de conexión", $conexionInfo);
    
    // Mostrar enlace para probar diferentes formatos de conexión
    echo "<div style='margin-top: 30px; text-align: center;'>";
    echo "<a href='test_conexion_exitosa.php' class='btn'>Probar diferentes formatos de conexión</a>";
    echo "</div>";
}

// Pie de página
echo "<div class='footer'>
        <p>&copy; " . date('Y') . " Sistema de Reportes de Sueldos. Todos los derechos reservados.</p>
    </div>
</body>
</html>";
?>
