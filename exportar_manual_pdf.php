<?php
// Incluir archivos necesarios
require_once 'vendor/autoload.php';
require_once 'permisos.php';
require_once 'auth.php';

// Verificar si el usuario está autenticado
requireLogin();

// Crear instancia de Dompdf
use Dompdf\Dompdf;
use Dompdf\Options;

// Configurar opciones
$options = new Options();
$options->set('isHtml5ParserEnabled', true);
$options->set('isPhpEnabled', true);
$options->set('isRemoteEnabled', true);
$options->set('defaultFont', 'Arial');

// Crear instancia de Dompdf con las opciones
$dompdf = new Dompdf($options);

// Obtener el contenido del archivo HTML
$html = file_get_contents('manual_usuario.html');

// Cargar el HTML en Dompdf
$dompdf->loadHtml($html);

// Establecer el tamaño del papel y orientación
$dompdf->setPaper('A4', 'portrait');

// Renderizar el PDF
$dompdf->render();

// Generar nombre de archivo
$filename = 'Manual_Usuario_Sistema_Reportes_' . date('Ymd') . '.pdf';

// Enviar el PDF al navegador
$dompdf->stream($filename, array('Attachment' => true));
exit;
?>
