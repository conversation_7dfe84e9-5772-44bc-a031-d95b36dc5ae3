<?php
// Incluir el archivo de conexiones alternativas
require_once 'CONEXION_ALTERNATIVAS.PHP';

// Establecer el modo de visualización de errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Prueba de Conexiones Alternativas a Oracle</h1>";

// Probar todas las conexiones
$resultados = probarTodasLasConexiones();

// Mostrar resultados
echo "<h2>Resultados de las pruebas</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Tipo de conexión</th><th>Resultado</th></tr>";

foreach ($resultados as $tipo => $resultado) {
    $color = (strpos($resultado, "Conexión exitosa") !== false) ? "green" : "red";
    echo "<tr>";
    echo "<td><strong>$tipo</strong></td>";
    echo "<td style='color: $color;'>$resultado</td>";
    echo "</tr>";
}

echo "</table>";

// Mostrar instrucciones para actualizar el archivo de conexión
echo "<h2>Instrucciones para actualizar el archivo de conexión</h2>";
echo "<p>Si alguna de las conexiones anteriores fue exitosa, puedes actualizar el archivo CONEXION.PHP para usar esa configuración:</p>";

echo "<h3>Opción 1: Usando SID</h3>";
echo "<pre>";
echo '<?php
// Configuración de la conexión a Oracle
$host = \'**********\';
$port = \'1521\';
$sid = \'DESA920\';
$user = \'EXP5\';
$pass = \'E*x20_22.\';

// Cadena de conexión usando SID
$dsn = "oci:dbname=$host:$port:$sid";

// Función para obtener una conexión PDO
function getConexion() {
    global $dsn, $user, $pass;
    try {
        $pdo = new PDO($dsn, $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("Error de conexión: " . $e->getMessage());
    }
}
?>';
echo "</pre>";

echo "<h3>Opción 2: Usando Service Name</h3>";
echo "<pre>";
echo '<?php
// Configuración de la conexión a Oracle
$host = \'**********\';
$port = \'1521\';
$service = \'DESA920\';
$user = \'EXP5\';
$pass = \'E*x20_22.\';

// Cadena de conexión usando Service Name
$dsn = "oci:dbname=//$host:$port/$service";

// Función para obtener una conexión PDO
function getConexion() {
    global $dsn, $user, $pass;
    try {
        $pdo = new PDO($dsn, $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("Error de conexión: " . $e->getMessage());
    }
}
?>';
echo "</pre>";

echo "<h3>Opción 3: Usando TNS</h3>";
echo "<pre>";
echo '<?php
// Configuración de la conexión a Oracle
$user = \'EXP5\';
$pass = \'E*x20_22.\';

// Cadena de conexión usando TNS
$dsn = "oci:dbname=DESA920";

// Función para obtener una conexión PDO
function getConexion() {
    global $dsn, $user, $pass;
    try {
        $pdo = new PDO($dsn, $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("Error de conexión: " . $e->getMessage());
    }
}
?>';
echo "</pre>";

echo "<h3>Opción 4: Usando formato completo</h3>";
echo "<pre>";
echo '<?php
// Configuración de la conexión a Oracle
$host = \'**********\';
$port = \'1521\';
$service = \'DESA920\';
$user = \'EXP5\';
$pass = \'E*x20_22.\';

// Cadena de conexión usando formato completo
$dsn = "oci:dbname=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=$host)(PORT=$port))(CONNECT_DATA=(SERVICE_NAME=$service)))";

// Función para obtener una conexión PDO
function getConexion() {
    global $dsn, $user, $pass;
    try {
        $pdo = new PDO($dsn, $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("Error de conexión: " . $e->getMessage());
    }
}
?>';
echo "</pre>";

echo "<h3>Opción 5: Usando SID con formato alternativo</h3>";
echo "<pre>";
echo '<?php
// Configuración de la conexión a Oracle
$host = \'**********\';
$port = \'1521\';
$sid = \'DESA920\';
$user = \'EXP5\';
$pass = \'E*x20_22.\';

// Cadena de conexión usando SID con formato alternativo
$dsn = "oci:dbname={$host}:{$port}/{$sid}";

// Función para obtener una conexión PDO
function getConexion() {
    global $dsn, $user, $pass;
    try {
        $pdo = new PDO($dsn, $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("Error de conexión: " . $e->getMessage());
    }
}
?>';
echo "</pre>";
?>
