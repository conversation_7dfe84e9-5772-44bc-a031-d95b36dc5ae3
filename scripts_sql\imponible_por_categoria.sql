-- Reporte de Imponible por Categoría
-- Este reporte muestra el imponible agrupado por categoría de empleados
-- Tablespace: SUELDOS27+

SELECT 
    a.CATEG as "CATEG<PERSON><PERSON>",
    a.ESCALAFON as "ESCALAFON",
    COUNT(a.PADROND) as "CA<PERSON><PERSON><PERSON>_EMPLEADOS",
    SUM(a.TACAREDU) as "TOTAL_IMPONIBLE",
    AVG(a.TACAREDU) as "PROMEDIO_IMPONIBLE",
    SUM(ASIGAPOR) as "TOTAL_ASIGNACIONES",
    AVG(ASIGAPOR) as "PROMEDIO_ASIGNACIONES",
    SUM(APORJUB) as "TOTAL_APORTES_JUB",
    AV<PERSON>(APORJUB) as "PROMEDIO_APORTES_JUB"
FROM 
    sueldo0425 a
JOIN 
    imponi0425B b ON a.PADROND = b.PADROND
GROUP BY 
    a.CATEG, a.ESCALAFON
ORDER BY 
    a.ESCALAFON, a.CATEG
