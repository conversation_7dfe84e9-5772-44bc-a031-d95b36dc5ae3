-- Script para el reporte de Falla de <PERSON>aja

-- Consulta final
select a.centro,C.<PERSON>MCENTRO,a.sector,C.NOMSECTOR,a.padrond,a.cuil,a.a<PERSON><PERSON>,a.categ,
<PERSON><PERSON>,<PERSON><PERSON>,
NVL(TO_CHAR(b.A05),'0,00') AS A05,
NVL(TO_CHAR(b.A79),'0,00') AS A79
--Nvl(A05, 0) + Nvl(A79, 0) Suma
from sueldo0425 a
join
(

SELECT TO_CHAR (a.CENTRO, '00') CENTRO,TO_CHAR (A.sector, '000') SECTOR,TO_CHAR(A.padrond,'0000000') padron,B.*
FROM exp5.sueldo0425 A
join
(
select * from (
select padronD,codigo,importe from EXP5.CIESCU0425
)
PIVOT
(
    sum(importe)
    FOR codigo IN (
'A05' A05,
'A79' A79
))
)
B ON (b.padrond=a.padrond) --(B.Q38>0 OR B.Q39>0 OR B.Q40>0 OR B.Q62>0 OR B.Q63>0);--; ---and a01>0 and a27>0;
UNION
SELECT TO_CHAR (a.CENTRO, '00') CENTRO,TO_CHAR (A.sector, '000') SECTOR,TO_CHAR(A.padrond,'0000000') padron,B.*
FROM exp5.sueldo0425 A
join
(
select * from (
select padronD,codigo,importe from EXP5.CIMENS0425
)
PIVOT
(
    sum(importe)
    FOR codigo IN (
'A05' A05,
'A79' A79
))
)
B ON (b.padrond=a.padrond) --(B.Q38>0 OR B.Q39>0 OR B.Q40>0 OR B.Q62>0 OR B.Q63>0);--; ---and a01>0 and a27>0;
)


B ON (b.padrond=a.padrond)  AND (B.A05>0 OR B.A79>0 OR B.A05<0 OR B.A79<0 )

JOIN
REPARTICIONES C
ON a.padrond=b.padrond AND A.CENTRO=C.CENTRO AND A.SECTOR=C.SECTOR
