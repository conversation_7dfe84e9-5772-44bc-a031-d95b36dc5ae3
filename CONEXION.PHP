<?php
// Configuración de la conexión a Oracle
$host = '**********';
$port = '1521';
$service = 'DESA920';
$sid = 'DESA920';  // Agregamos SID por si es necesario
$user = 'EXP5';
$pass = 'E*x20_22.';

// Cadenas de conexión alternativas
// Opción 1: Usando Service Name (formato actual)
$dsn_service = "oci:dbname=//{$host}:{$port}/{$service};charset=AL32UTF8";

// Opción 2: Usando SID
$dsn_sid = "oci:dbname={$host}:{$port}/{$sid};charset=AL32UTF8";

// Opción 3: Usando TNS
$dsn_tns = "oci:dbname=DESA920";

// Opción 4: Usando formato completo
$dsn_full = "oci:dbname=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST={$host})(PORT={$port}))(CONNECT_DATA=(SERVICE_NAME={$service})))";

// Cadena de conexión a utilizar (probamos diferentes opciones)
// Opción 1: Service Name
// $dsn = $dsn_service;

// Opción 2: SID (esta suele funcionar mejor en muchos casos)
$dsn = $dsn_sid;

// Opción 3: TNS (requiere archivo tnsnames.ora configurado)
// $dsn = $dsn_tns;

// Opción 4: Formato completo
// $dsn = $dsn_full;

// Función para obtener una conexión PDO
function getConexion() {
    global $dsn, $user, $pass;
    
    try {
        // Crear una nueva conexión PDO
        $pdo = new PDO($dsn, $user, $pass);
        
        // Configurar el modo de error para que lance excepciones
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Configurar el modo de recuperación para que devuelva arrays asociativos
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        
        // Configurar el modo de caso para que los nombres de columnas sean en mayúsculas
        $pdo->setAttribute(PDO::ATTR_CASE, PDO::CASE_UPPER);
        
        // Configurar el modo de nulos para que los valores nulos se conviertan a cadenas vacías
        $pdo->setAttribute(PDO::ATTR_ORACLE_NULLS, PDO::NULL_TO_STRING);
        
        return $pdo;
    } catch (PDOException $e) {
        // Registrar el error en el log
        error_log("Error de conexión a la base de datos: " . $e->getMessage());
        
        // Intentar con formatos alternativos si el formato principal falla
        try {
            // Intentar con formato 2
            global $dsn_easy_service, $user, $pass;
            $pdo = new PDO($dsn_easy_service, $user, $pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            $pdo->setAttribute(PDO::ATTR_CASE, PDO::CASE_UPPER);
            $pdo->setAttribute(PDO::ATTR_ORACLE_NULLS, PDO::NULL_TO_STRING);
            error_log("Conexión exitosa usando formato alternativo 2");
            return $pdo;
        } catch (PDOException $e2) {
            try {
                // Intentar con formato 3
                global $dsn_traditional, $user, $pass;
                $pdo = new PDO($dsn_traditional, $user, $pass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
                $pdo->setAttribute(PDO::ATTR_CASE, PDO::CASE_UPPER);
                $pdo->setAttribute(PDO::ATTR_ORACLE_NULLS, PDO::NULL_TO_STRING);
                error_log("Conexión exitosa usando formato alternativo 3");
                return $pdo;
            } catch (PDOException $e3) {
                try {
                    // Intentar con formato 5
                    global $dsn_full, $user, $pass;
                    $pdo = new PDO($dsn_full, $user, $pass);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
                    $pdo->setAttribute(PDO::ATTR_CASE, PDO::CASE_UPPER);
                    $pdo->setAttribute(PDO::ATTR_ORACLE_NULLS, PDO::NULL_TO_STRING);
                    error_log("Conexión exitosa usando formato alternativo 5");
                    return $pdo;
                } catch (PDOException $e4) {
                    // Si todos los formatos fallan, lanzar la excepción original
                    throw $e;
                }
            }
        }
    }
}
?>
