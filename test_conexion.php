<?php
// Incluir el archivo de conexión
require_once 'CONEXION.PHP';

// Establecer el modo de visualización de errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Prueba de Conexión a Oracle</h1>";

try {
    // Intentar obtener una conexión
    $pdo = getConexion();
    
    // Si llegamos aquí, la conexión fue exitosa
    echo "<p style='color: green; font-weight: bold;'>Conexión exitosa a la base de datos Oracle!</p>";
    
    // Mostrar información de la conexión
    echo "<h2>Información de la conexión</h2>";
    echo "<ul>";
    echo "<li><strong>Versión de Oracle:</strong> " . $pdo->getAttribute(PDO::ATTR_SERVER_VERSION) . "</li>";
    echo "<li><strong>Versión del cliente:</strong> " . $pdo->getAttribute(PDO::ATTR_CLIENT_VERSION) . "</li>";
    echo "<li><strong>Nombre de la conexión:</strong> " . $pdo->getAttribute(PDO::ATTR_CONNECTION_STATUS) . "</li>";
    echo "</ul>";
    
    // Realizar una consulta simple para verificar que la conexión funciona
    echo "<h2>Prueba de consulta</h2>";
    
    // Consulta simple para obtener la fecha y hora del servidor
    $stmt = $pdo->query("SELECT SYSDATE FROM DUAL");
    $fecha = $stmt->fetchColumn();
    
    echo "<p><strong>Fecha y hora del servidor:</strong> $fecha</p>";
    
    // Consulta para obtener información de las tablas
    echo "<h2>Tablas disponibles</h2>";
    
    $stmt = $pdo->query("SELECT TABLE_NAME FROM USER_TABLES WHERE ROWNUM <= 10");
    
    echo "<ul>";
    while ($row = $stmt->fetch()) {
        echo "<li>" . $row['TABLE_NAME'] . "</li>";
    }
    echo "</ul>";
    
} catch (PDOException $e) {
    // Mostrar el error
    echo "<p style='color: red; font-weight: bold;'>Error de conexión: " . $e->getMessage() . "</p>";
    
    // Mostrar información adicional para depuración
    echo "<h2>Información de depuración</h2>";
    echo "<ul>";
    echo "<li><strong>Código de error:</strong> " . $e->getCode() . "</li>";
    echo "<li><strong>Archivo:</strong> " . $e->getFile() . "</li>";
    echo "<li><strong>Línea:</strong> " . $e->getLine() . "</li>";
    echo "</ul>";
    
    // Mostrar la traza de la pila
    echo "<h3>Traza de la pila</h3>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    
    // Mostrar sugerencias para solucionar el problema
    echo "<h2>Sugerencias para solucionar el problema</h2>";
    
    // Verificar si es un error ORA-12514
    if (strpos($e->getMessage(), 'ORA-12514') !== false) {
        echo "<p>El error ORA-12514 indica que el listener de Oracle no puede encontrar el servicio solicitado. Posibles soluciones:</p>";
        echo "<ol>";
        echo "<li>Verifica que el nombre del servicio 'DESA920' sea correcto.</li>";
        echo "<li>Asegúrate de que el servicio Oracle esté en ejecución en el servidor.</li>";
        echo "<li>Verifica que el listener de Oracle esté configurado correctamente.</li>";
        echo "<li>Prueba usando un SID en lugar de un Service Name.</li>";
        echo "</ol>";
        
        // Mostrar configuración actual
        echo "<h3>Configuración actual</h3>";
        echo "<pre>";
        echo "Host: $host\n";
        echo "Puerto: $port\n";
        echo "Servicio: $service\n";
        echo "Usuario: $user\n";
        echo "DSN: $dsn\n";
        echo "</pre>";
        
        // Sugerir configuraciones alternativas
        echo "<h3>Configuraciones alternativas</h3>";
        echo "<p>Prueba con las siguientes configuraciones alternativas:</p>";
        
        echo "<h4>Opción 1: Usando SID</h4>";
        echo "<pre>oci:dbname=$host:$port:DESA920</pre>";
        
        echo "<h4>Opción 2: Usando Service Name</h4>";
        echo "<pre>oci:dbname=//$host:$port/DESA920</pre>";
        
        echo "<h4>Opción 3: Usando formato completo</h4>";
        echo "<pre>oci:dbname=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=$host)(PORT=$port))(CONNECT_DATA=(SERVICE_NAME=DESA920)))</pre>";
    }
}
?>
