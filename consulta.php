<?php
// Incluir archivo de permisos
require_once 'permisos.php';

// Verificar que el usuario esté autenticado y tenga permiso
requireLogin();
requirePermiso('ver_consulta_hidraulica');
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Imponible Hidráulica - Centro 22</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <header>
        <div class="container">
            <h1>Sistema de Consultas Oracle</h1>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul>
                <li><a href="index.php">Inicio</a></li>
                <?php
                // Verificar si el usuario tiene permiso para ver al menos una consulta
                $mostrarReportes = tienePermiso('ver_reportes');
                if (!$mostrarReportes) {
                    // Verificar permisos específicos para consultas
                    if (tienePermiso('ver_consulta_hidraulica') || tienePermiso('ver_consulta_policia')) {
                        $mostrarReportes = true;
                    }
                }
                if ($mostrarReportes):
                ?>
                    <li><a href="reportes.php">Reportes</a></li>
                <?php endif; ?>
                <?php if (tienePermiso('admin_usuarios')): ?>
                    <li><a href="admin_usuarios.php">Administrar Usuarios</a></li>
                    <li><a href="admin_permisos_usuario.php">Administrar Permisos</a></li>
                <?php endif; ?>
                <li><a href="logout.php">Cerrar Sesión (<?php echo htmlspecialchars($_SESSION['user_username']); ?>)</a></li>
            </ul>
        </div>
    </nav>

    <main class="container">
        <?php
        try {
            $pdo = getConexion();

            echo "<h2>Imponible Hidraulica - Centro 22</h2>";

            $sql = "SELECT
                        a.CENTRO,
                        a.SECTOR,
                        a.PADROND,
                        a.APYNOM,
                        a.CUIL,
                        a.ANTIG,
                        a.PERM_CAT,
                        a.CATEG,
                        a.ESCALAFON,
                        a.FNACIM,
                        ASIGAPOR,
                        RETROMESANT,
                        OTRORETRO,
                        OTROCONCEPSINAPO,
                        SALARIOSINAPO,
                        APORJUB,
                        H01,
                        Z01
                    FROM sueldo0425 a
                    JOIN imponi0425B b ON a.PADROND = b.PADROND
                    WHERE a.CENTRO = 22
                    ORDER BY a.CENTRO, a.SECTOR, a.PADROND";

            $stmt = $pdo->query($sql);

            echo "<table>";
            echo "<tr>
                    <th>CENTRO</th>
                    <th>SECTOR</th>
                    <th>PADRON</th>
                    <th>NOMBRE</th>
                    <th>CUIL</th>
                    <th>ANTIG</th>
                    <th>PERM_CAT</th>
                    <th>CATEG</th>
                    <th>ESCALAFÓN</th>
                    <th>FECHA NAC.</th>
                    <th>ASIG. APOR</th>
                    <th>RETRO MES ANT</th>
                    <th>OTRO RETRO</th>
                    <th>OTROS CONCEP. SIN APORTE</th>
                    <th>SALARIO SIN APORTE</th>
                    <th>APORT. JUB.</th>
                    <th>H01</th>
                    <th>Z01</th>
                  </tr>";

            foreach ($stmt as $fila) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($fila['CENTRO']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['SECTOR']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['PADROND']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['APYNOM']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['CUIL']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['ANTIG']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['PERM_CAT']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['CATEG']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['ESCALAFON']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['FNACIM']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['ASIGAPOR']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['RETROMESANT']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['OTRORETRO']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['OTROCONCEPSINAPO']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['SALARIOSINAPO']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['APORJUB']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['H01']) . "</td>";
                echo "<td>" . htmlspecialchars($fila['Z01']) . "</td>";
                echo "</tr>";
            }

            echo "</table>";

            if (isset($_POST['exportar'])) {
                // Incluir directamente los archivos de PHPExcel
                require_once 'PHPExcel.php';
                require_once 'PHPExcel/IOFactory.php';

                $objPHPExcel = new PHPExcel();
                $objPHPExcel->setActiveSheetIndex(0);
                $sheet = $objPHPExcel->getActiveSheet();

                // Encabezados
                $headers = [
                    'CENTRO', 'SECTOR', 'PADRON', 'NOMBRE', 'CUIL', 'ANTIG', 'PERM_CAT', 'CATEG', 'ESCALAFÓN',
                    'FECHA NAC.', 'ASIG. APOR', 'RETRO MES ANT', 'OTRO RETRO', 'OTROS CONCEP. SIN APORTE',
                    'SALARIO SIN APORTE', 'APORT. JUB.', 'H01', 'Z01'
                ];
                $col = 0;
                foreach ($headers as $header) {
                    $sheet->setCellValueByColumnAndRow($col++, 1, $header);
                }

                // Cuerpo - Ejecutamos la consulta nuevamente para obtener datos frescos
                $stmtExcel = $pdo->query($sql);
                $row = 2;
                while ($fila = $stmtExcel->fetch(PDO::FETCH_ASSOC)) {
                    $col = 0;
                    foreach ($headers as $key) {
                        // Normalizamos los nombres de los campos para coincidir con claves reales
                        $campo = strtoupper(str_replace(['.', ' '], '', $key));
                        $sheet->setCellValueByColumnAndRow($col++, $row, $fila[$campo]);
                    }
                    $row++;
                }

                // Encabezados para descarga
                header('Content-Type: application/vnd.ms-excel');
                header('Content-Disposition: attachment;filename="imponible_hidraulica.xls"');
                header('Cache-Control: max-age=0');

                // Limpiar cualquier salida previa
                ob_end_clean();

                // Usar formato Excel5 (.xls) en lugar de Excel2007 (.xlsx)
                $writer = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
                $writer->save('php://output');
                exit;
            }

        } catch (PDOException $e) {
            echo "<p class='error'>Error en la conexión o la consulta: " . $e->getMessage() . "</p>";
        }
        ?>

        <form method="post">
            <button type="submit" name="exportar" class="btn">Exportar a Excel</button>
            <a href="index.php" class="btn" style="margin-left: 10px;">Volver al Inicio</a>
        </form>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Sistema de Consultas Oracle. Todos los derechos reservados.</p>
        </div>
    </footer>
</body>
</html>
