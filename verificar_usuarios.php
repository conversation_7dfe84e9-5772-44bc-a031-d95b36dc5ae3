<?php
// Incluir archivo de conexión
require_once 'CONEXION.PHP';

// Establecer el modo de visualización de errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Verificación de Usuarios</h1>";

try {
    // Obtener conexión a la base de datos
    $pdo = getConexion();
    
    echo "<p style='color: green; font-weight: bold;'>Conexión a la base de datos exitosa!</p>";
    
    // Verificar si la tabla de usuarios existe
    try {
        $sql = "SELECT COUNT(*) FROM usuarios";
        $stmt = $pdo->query($sql);
        $count = $stmt->fetchColumn();
        
        echo "<p style='color: green; font-weight: bold;'>La tabla de usuarios existe!</p>";
        echo "<p>Número de usuarios registrados: $count</p>";
        
        // Mostrar los usuarios registrados
        if ($count > 0) {
            $sql = "SELECT id, username, nombre, email, rol, activo FROM usuarios";
            $stmt = $pdo->query($sql);
            $usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h2>Usuarios Registrados</h2>";
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>ID</th><th>Usuario</th><th>Nombre</th><th>Email</th><th>Rol</th><th>Activo</th></tr>";
            
            foreach ($usuarios as $usuario) {
                echo "<tr>";
                echo "<td>" . (isset($usuario['ID']) ? $usuario['ID'] : $usuario['id']) . "</td>";
                echo "<td>" . (isset($usuario['USERNAME']) ? $usuario['USERNAME'] : $usuario['username']) . "</td>";
                echo "<td>" . (isset($usuario['NOMBRE']) ? $usuario['NOMBRE'] : $usuario['nombre']) . "</td>";
                echo "<td>" . (isset($usuario['EMAIL']) ? $usuario['EMAIL'] : $usuario['email']) . "</td>";
                echo "<td>" . (isset($usuario['ROL']) ? $usuario['ROL'] : $usuario['rol']) . "</td>";
                echo "<td>" . (isset($usuario['ACTIVO']) ? ($usuario['ACTIVO'] ? 'Sí' : 'No') : ($usuario['activo'] ? 'Sí' : 'No')) . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red; font-weight: bold;'>La tabla de usuarios no existe!</p>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        
        // Crear la tabla de usuarios
        echo "<h2>Crear Tabla de Usuarios</h2>";
        echo "<p>Para crear la tabla de usuarios, ejecuta el siguiente SQL:</p>";
        echo "<pre>";
        echo "CREATE TABLE usuarios (
    id NUMBER PRIMARY KEY,
    username VARCHAR2(50) NOT NULL UNIQUE,
    password VARCHAR2(255) NOT NULL,
    nombre VARCHAR2(100) NOT NULL,
    email VARCHAR2(100),
    rol VARCHAR2(20) DEFAULT 'usuario',
    fecha_creacion DATE DEFAULT SYSDATE,
    activo NUMBER(1) DEFAULT 1
);

CREATE SEQUENCE usuarios_seq START WITH 1 INCREMENT BY 1;

CREATE OR REPLACE TRIGGER usuarios_trg
BEFORE INSERT ON usuarios
FOR EACH ROW
BEGIN
    SELECT usuarios_seq.NEXTVAL INTO :new.id FROM dual;
END;
";
        echo "</pre>";
        
        // Formulario para crear la tabla
        echo "<form method='post'>";
        echo "<input type='hidden' name='crear_tabla' value='1'>";
        echo "<button type='submit'>Crear Tabla de Usuarios</button>";
        echo "</form>";
    }
    
    // Formulario para crear un usuario administrador
    echo "<h2>Crear Usuario Administrador</h2>";
    echo "<form method='post'>";
    echo "<div style='margin-bottom: 10px;'>";
    echo "<label for='username'>Usuario:</label>";
    echo "<input type='text' id='username' name='username' value='admin'>";
    echo "</div>";
    echo "<div style='margin-bottom: 10px;'>";
    echo "<label for='password'>Contraseña:</label>";
    echo "<input type='password' id='password' name='password' value='admin123'>";
    echo "</div>";
    echo "<div style='margin-bottom: 10px;'>";
    echo "<label for='nombre'>Nombre:</label>";
    echo "<input type='text' id='nombre' name='nombre' value='Administrador'>";
    echo "</div>";
    echo "<div style='margin-bottom: 10px;'>";
    echo "<label for='email'>Email:</label>";
    echo "<input type='email' id='email' name='email' value='<EMAIL>'>";
    echo "</div>";
    echo "<input type='hidden' name='crear_admin' value='1'>";
    echo "<button type='submit'>Crear Usuario Administrador</button>";
    echo "</form>";
    
} catch (PDOException $e) {
    echo "<p style='color: red; font-weight: bold;'>Error de conexión a la base de datos!</p>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}

// Procesar formulario para crear la tabla de usuarios
if (isset($_POST['crear_tabla'])) {
    try {
        $pdo = getConexion();
        
        // Crear la tabla de usuarios
        $sql = "CREATE TABLE usuarios (
            id NUMBER PRIMARY KEY,
            username VARCHAR2(50) NOT NULL UNIQUE,
            password VARCHAR2(255) NOT NULL,
            nombre VARCHAR2(100) NOT NULL,
            email VARCHAR2(100),
            rol VARCHAR2(20) DEFAULT 'usuario',
            fecha_creacion DATE DEFAULT SYSDATE,
            activo NUMBER(1) DEFAULT 1
        )";
        $pdo->exec($sql);
        
        // Crear la secuencia
        $sql = "CREATE SEQUENCE usuarios_seq START WITH 1 INCREMENT BY 1";
        $pdo->exec($sql);
        
        // Crear el trigger
        $sql = "CREATE OR REPLACE TRIGGER usuarios_trg
        BEFORE INSERT ON usuarios
        FOR EACH ROW
        BEGIN
            SELECT usuarios_seq.NEXTVAL INTO :new.id FROM dual;
        END;";
        $pdo->exec($sql);
        
        echo "<p style='color: green; font-weight: bold;'>Tabla de usuarios creada correctamente!</p>";
        echo "<p>Recarga la página para verificar.</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red; font-weight: bold;'>Error al crear la tabla de usuarios!</p>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
    }
}

// Procesar formulario para crear un usuario administrador
if (isset($_POST['crear_admin'])) {
    try {
        $pdo = getConexion();
        
        $username = $_POST['username'];
        $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
        $nombre = $_POST['nombre'];
        $email = $_POST['email'];
        $rol = 'admin';
        
        // Verificar si el usuario ya existe
        $sql = "SELECT COUNT(*) FROM usuarios WHERE username = :username";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        if ($stmt->fetchColumn() > 0) {
            echo "<p style='color: red; font-weight: bold;'>El usuario '$username' ya existe!</p>";
        } else {
            // Insertar el usuario administrador
            $sql = "INSERT INTO usuarios (username, password, nombre, email, rol) VALUES (:username, :password, :nombre, :email, :rol)";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':password', $password);
            $stmt->bindParam(':nombre', $nombre);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':rol', $rol);
            $stmt->execute();
            
            echo "<p style='color: green; font-weight: bold;'>Usuario administrador creado correctamente!</p>";
            echo "<p>Usuario: $username</p>";
            echo "<p>Contraseña: " . $_POST['password'] . "</p>";
            echo "<p>Recarga la página para verificar.</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red; font-weight: bold;'>Error al crear el usuario administrador!</p>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
    }
}
?>
