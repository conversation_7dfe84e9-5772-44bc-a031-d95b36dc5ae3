<?php
// Incluir archivo de autenticación
require_once 'auth.php';

// Si el usuario ya está autenticado, redirigir a la página principal
if (isLoggedIn()) {
    header('Location: index.php');
    exit;
}
$error = '';
$mensaje = '';

// Verificar si hay un mensaje en la sesión
if (isset($_SESSION['mensaje'])) {
    $mensaje = $_SESSION['mensaje'];
    unset($_SESSION['mensaje']);
}

// Procesar el formulario de login
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';

    if (empty($username) || empty($password)) {
        $error = 'Por favor, ingrese usuario y contraseña';
    } else {
        if (login($username, $password)) {
            header('Location: index.php');
            exit;
        } else {
            $error = 'Usuario o contraseña incorrectos';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar Sesión - Sistema de Consultas Oracle</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .login-container {
            max-width: 450px; /* Aumentado de 400px a 450px para dar más espacio */
            margin: 50px auto;
            background-color: white;
            padding: 30px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .logo-container {
            margin-bottom: 20px;
            display: flex;
            justify-content: center;
        }

        .logo-container img {
            max-width: 300px; /* Aumentado a 300px para una imagen más grande */
            height: auto;
            margin-bottom: 10px; /* Añadido espacio debajo de la imagen */
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 16px;
        }

        .btn-login {
            width: 100%;
            padding: 12px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 3px;
            font-size: 16px;
            cursor: pointer;
        }

        .btn-login:hover {
            background-color: #2980b9;
        }

        .error-message {
            color: #e74c3c;
            margin-bottom: 20px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    

    <main class="container">
        <div class="login-container">
            <div class="logo-container">
                <!-- Cambia 'mi_logo.jpg' por el nombre exacto de tu archivo de imagen -->
                <img src="images/mi_logo.jpg" alt="Logo del Sistema">
            </div>

            <h2>Iniciar Sesión</h2>

            <?php if (!empty($error)): ?>
                <div class="error-message"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if (!empty($mensaje)): ?>
                <div class="success-message" style="color: #27ae60; margin-bottom: 20px; font-weight: bold;">
                    <?php echo $mensaje; ?>
                </div>
            <?php endif; ?>

            <form method="post">
                <div class="form-group">
                    <label for="username">Usuario</label>
                    <input type="text" id="username" name="username" required>
                </div>

                <div class="form-group">
                    <label for="password">Contraseña</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <button type="submit" class="btn-login">Iniciar Sesión</button>
            </form>
        </div>
    </main>

    
</body>
</html>
