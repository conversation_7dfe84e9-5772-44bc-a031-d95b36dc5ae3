<?php
// Incluir archivos necesarios
require_once 'conexion.php';
require_once 'permisos.php';
require_once 'auth.php';

// Verificar que el usuario esté autenticado y tenga permiso
requireLogin();
requirePermiso('admin_usuarios');

// Mensaje de éxito o error
$mensaje = '';
$error = '';

// Buscar el usuario dpohidraulica
try {
    $pdo = getConexion();
    $sql = "SELECT id, username, nombre, rol FROM usuarios WHERE username = 'dpohidraulica'";
    $stmt = $pdo->query($sql);

    if ($stmt->rowCount() > 0) {
        $usuario = $stmt->fetch(PDO::FETCH_ASSOC);
        $userId = isset($usuario['ID']) ? $usuario['ID'] : $usuario['id'];
        $username = isset($usuario['USERNAME']) ? $usuario['USERNAME'] : $usuario['username'];
        $nombre = isset($usuario['NOMBRE']) ? $usuario['NOMBRE'] : $usuario['nombre'];
        $rol = isset($usuario['ROL']) ? $usuario['ROL'] : $usuario['rol'];

        $mensaje = "Usuario dpohidraulica encontrado con ID: $userId";

        // Obtener permisos actuales
        $permisosActuales = getPermisosUsuario($userId, true);

        // Verificar si se ha enviado el formulario
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['asignar_permisos'])) {
            // Permisos a asignar
            $permisos = [
                'exportar_excel',
                'ver_reporte_hidraulica',
                'ver_reportes'
            ];

            // Guardar permisos
            $resultado = guardarPermisosUsuario($userId, $permisos);

            if ($resultado === true) {
                $mensaje = "Permisos asignados correctamente al usuario dpohidraulica";

                // Actualizar permisos actuales
                $permisosActuales = getPermisosUsuario($userId, true);

                // Limpiar la caché de permisos
                if (isset($_SESSION['permisos_cache'])) {
                    unset($_SESSION['permisos_cache']);
                }
            } else {
                $error = "Error al asignar permisos: $resultado";
            }
        }
    } else {
        $error = "El usuario dpohidraulica no existe en la base de datos";

        // Formulario para crear el usuario
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['crear_usuario'])) {
            // Datos del usuario
            $username = 'dpohidraulica';
            $password = password_hash('dpohidraulica123', PASSWORD_DEFAULT);
            $nombre = 'DEPARTAMENTO HIDRAULICA';
            $email = '<EMAIL>';
            $rol = 'usuario';

            // Insertar el usuario
            $sql = "INSERT INTO usuarios (username, password, nombre, email, rol) VALUES (:username, :password, :nombre, :email, :rol)";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':password', $password);
            $stmt->bindParam(':nombre', $nombre);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':rol', $rol);

            if ($stmt->execute()) {
                $userId = $pdo->lastInsertId();
                $mensaje = "Usuario dpohidraulica creado correctamente con ID: $userId";

                // Asignar permisos
                $permisos = [
                    'exportar_excel',
                    'ver_reporte_hidraulica',
                    'ver_reportes'
                ];

                $resultado = guardarPermisosUsuario($userId, $permisos);

                if ($resultado === true) {
                    $mensaje .= " y permisos asignados correctamente";

                    // Obtener permisos actuales
                    $permisosActuales = getPermisosUsuario($userId, true);

                    // Limpiar la caché de permisos
                    if (isset($_SESSION['permisos_cache'])) {
                        unset($_SESSION['permisos_cache']);
                    }
                } else {
                    $error = "Error al asignar permisos: $resultado";
                }
            } else {
                $error = "Error al crear el usuario dpohidraulica";
            }
        }

        // Mostrar todos los usuarios disponibles
        $sql = "SELECT id, username, nombre, rol FROM usuarios ORDER BY id";
        $stmt = $pdo->query($sql);
        $usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    $error = "Error en la base de datos: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <?php include 'head_common.php'; ?>
    <title>Asignar Permisos a dpohidraulica - Sistema de Reportes de Sueldos</title>
</head>
<body>
<?php include 'header_with_user.php'; ?>

    <?php include 'nav_menu.php'; ?>

    <main class="container">
        <h2>Asignar Permisos a dpohidraulica</h2>

        <?php if (!empty($mensaje)): ?>
            <div class="success"><?php echo $mensaje; ?></div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if (isset($userId)): ?>
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                <h3>Información del Usuario</h3>
                <p><strong>ID:</strong> <?php echo $userId; ?></p>
                <p><strong>Usuario:</strong> <?php echo htmlspecialchars($username); ?></p>
                <p><strong>Nombre:</strong> <?php echo htmlspecialchars($nombre); ?></p>
                <p><strong>Rol:</strong> <?php echo htmlspecialchars($rol); ?></p>

                <h3>Permisos Actuales</h3>
                <?php if (!empty($permisosActuales)): ?>
                    <ul>
                        <?php foreach ($permisosActuales as $permiso): ?>
                            <li><?php echo htmlspecialchars($permiso); ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php else: ?>
                    <p>No tiene permisos asignados.</p>
                <?php endif; ?>

                <form method="post" style="margin-top: 20px;">
                    <button type="submit" name="asignar_permisos" class="btn btn-primary">Asignar Permisos Correctos</button>
                </form>
            </div>

            <p>
                <a href="admin_permisos_usuario.php?user_id=<?php echo $userId; ?>" class="btn">Administrar Permisos</a>
                <a href="reportes.php" class="btn">Ver Reportes</a>
                <a href="limpiar_sesion.php" class="btn btn-warning">Limpiar Caché y Reiniciar Sesión</a>
            </p>
        <?php else: ?>
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                <h3>Crear Usuario dpohidraulica</h3>
                <p>El usuario dpohidraulica no existe en la base de datos. Puedes crearlo con los permisos correctos.</p>

                <form method="post" style="margin-top: 20px;">
                    <button type="submit" name="crear_usuario" class="btn btn-primary">Crear Usuario dpohidraulica</button>
                </form>
            </div>

            <?php if (isset($usuarios) && !empty($usuarios)): ?>
                <h3>Usuarios Disponibles</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Usuario</th>
                            <th>Nombre</th>
                            <th>Rol</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($usuarios as $user): ?>
                            <tr>
                                <td><?php echo isset($user['ID']) ? $user['ID'] : $user['id']; ?></td>
                                <td><?php echo htmlspecialchars(isset($user['USERNAME']) ? $user['USERNAME'] : $user['username']); ?></td>
                                <td><?php echo htmlspecialchars(isset($user['NOMBRE']) ? $user['NOMBRE'] : $user['nombre']); ?></td>
                                <td><?php echo htmlspecialchars(isset($user['ROL']) ? $user['ROL'] : $user['rol']); ?></td>
                                <td>
                                    <a href="admin_permisos_usuario.php?user_id=<?php echo isset($user['ID']) ? $user['ID'] : $user['id']; ?>" class="btn btn-sm">Ver Permisos</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        <?php endif; ?>
    </main>
</body>
</html>
