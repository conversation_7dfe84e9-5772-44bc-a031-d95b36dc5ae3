<?php
/**
 * Configuración de logs para el sistema de reportes
 * 
 * Este archivo configura el sistema de logs para guardar todos los error_log
 * en un archivo de texto específico para facilitar el debugging.
 */

// Definir la ruta del archivo de logs
$log_directory = __DIR__ . '/logs';
$log_file = $log_directory . '/sistema_reportes.log';

// Crear el directorio de logs si no existe
if (!is_dir($log_directory)) {
    mkdir($log_directory, 0755, true);
}

// Configurar PHP para usar nuestro archivo de log personalizado
ini_set('log_errors', 1);
ini_set('error_log', $log_file);

/**
 * Función personalizada para escribir logs con formato mejorado
 * 
 * @param string $message Mensaje a escribir en el log
 * @param string $level Nivel del log (INFO, ERROR, DEBUG, WARNING)
 * @param string $context Contexto adicional (nombre del archivo, función, etc.)
 */
function writeLog($message, $level = 'INFO', $context = '') {
    global $log_file;
    
    $timestamp = date('Y-m-d H:i:s');
    $user = isset($_SESSION['user_username']) ? $_SESSION['user_username'] : 'GUEST';
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'UNKNOWN';
    
    // Formatear el mensaje
    $formatted_message = "[$timestamp] [$level] [USER:$user] [IP:$ip]";
    
    if (!empty($context)) {
        $formatted_message .= " [$context]";
    }
    
    $formatted_message .= " - $message" . PHP_EOL;
    
    // Escribir al archivo
    file_put_contents($log_file, $formatted_message, FILE_APPEND | LOCK_EX);
}

/**
 * Función para limpiar logs antiguos (mantener solo los últimos 30 días)
 */
function cleanOldLogs() {
    global $log_file;
    
    if (file_exists($log_file)) {
        $file_age = time() - filemtime($log_file);
        $thirty_days = 30 * 24 * 60 * 60; // 30 días en segundos
        
        if ($file_age > $thirty_days) {
            // Crear backup antes de limpiar
            $backup_file = $log_file . '.backup.' . date('Y-m-d');
            copy($log_file, $backup_file);
            
            // Limpiar el archivo principal
            file_put_contents($log_file, '');
            writeLog('Log file cleaned and backed up', 'INFO', 'SYSTEM');
        }
    }
}

/**
 * Función para obtener las últimas líneas del log
 * 
 * @param int $lines Número de líneas a obtener
 * @return array Array con las últimas líneas del log
 */
function getLastLogLines($lines = 100) {
    global $log_file;
    
    if (!file_exists($log_file)) {
        return [];
    }
    
    $file_lines = file($log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    
    if ($file_lines === false) {
        return [];
    }
    
    return array_slice($file_lines, -$lines);
}

/**
 * Función para buscar en los logs
 * 
 * @param string $search_term Término a buscar
 * @param int $max_results Máximo número de resultados
 * @return array Array con las líneas que contienen el término
 */
function searchLogs($search_term, $max_results = 50) {
    global $log_file;
    
    if (!file_exists($log_file)) {
        return [];
    }
    
    $file_lines = file($log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    
    if ($file_lines === false) {
        return [];
    }
    
    $results = [];
    $count = 0;
    
    // Buscar desde el final del archivo hacia atrás
    for ($i = count($file_lines) - 1; $i >= 0 && $count < $max_results; $i--) {
        if (stripos($file_lines[$i], $search_term) !== false) {
            $results[] = $file_lines[$i];
            $count++;
        }
    }
    
    return array_reverse($results); // Devolver en orden cronológico
}

// Limpiar logs antiguos al cargar la configuración
cleanOldLogs();

// Escribir un mensaje de inicio si es la primera vez que se carga
if (!file_exists($log_file) || filesize($log_file) == 0) {
    writeLog('Sistema de logs inicializado', 'INFO', 'SYSTEM');
}

?>
