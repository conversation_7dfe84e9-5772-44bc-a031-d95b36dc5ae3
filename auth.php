<?php
// Iniciar sesión si no está iniciada
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Incluir archivo de conexión
require_once 'conexion.php';

/**
 * Verifica si el usuario está autenticado
 * @return bool
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * Verifica si el usuario es administrador
 * @return bool
 */
function isAdmin() {
    return isLoggedIn() && $_SESSION['user_rol'] === 'admin';
}

/**
 * Redirige al usuario a la página de login si no está autenticado
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

/**
 * Redirige al usuario a la página principal si no es administrador
 */
function requireAdmin() {
    if (!isAdmin()) {
        header('Location: index.php');
        exit;
    }
}

/**
 * Intenta autenticar al usuario con las credenciales proporcionadas
 * @param string $username
 * @param string $password
 * @return bool
 */
function login($username, $password) {
    try {
        $pdo = getConexion();

        // Modificamos la consulta para quitar la restricción de activo=1 temporalmente
        $sql = "SELECT id, username, password, nombre, email, rol, activo FROM usuarios WHERE username = :username";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':username', $username);
        $stmt->execute();

        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            // En Oracle, los nombres de columnas se devuelven en mayúsculas
            $passwordHash = isset($user['PASSWORD']) ? $user['PASSWORD'] : $user['password'];
            $userId = isset($user['ID']) ? $user['ID'] : $user['id'];
            $userUsername = isset($user['USERNAME']) ? $user['USERNAME'] : $user['username'];
            $userNombre = isset($user['NOMBRE']) ? $user['NOMBRE'] : $user['nombre'];
            $userEmail = isset($user['EMAIL']) ? $user['EMAIL'] : $user['email'];
            $userRol = isset($user['ROL']) ? $user['ROL'] : $user['rol'];
            $userActivo = isset($user['ACTIVO']) ? $user['ACTIVO'] : $user['activo'];

            // Registrar información para depuración
            error_log("Intento de login para usuario: $userUsername");
            error_log("Password hash almacenado: $passwordHash");

            // Verificar si el usuario está activo (comentado temporalmente para pruebas)
            // if (!$userActivo) {
            //     error_log("Usuario inactivo: $userUsername");
            //     return false;
            // }

            // Verificar la contraseña
            if (password_verify($password, $passwordHash)) {
                error_log("Verificación de contraseña exitosa para: $userUsername");

                // Guardar datos del usuario en la sesión
                $_SESSION['user_id'] = $userId;
                $_SESSION['user_username'] = $userUsername;
                $_SESSION['user_nombre'] = $userNombre;
                $_SESSION['user_email'] = $userEmail;
                $_SESSION['user_rol'] = $userRol;

                return true;
            } else {
                error_log("Verificación de contraseña fallida para: $userUsername");

                // Para depuración: verificar si la contraseña coincide sin hash
                if ($password === $passwordHash) {
                    error_log("La contraseña coincide sin hash para: $userUsername");

                    // Guardar datos del usuario en la sesión
                    $_SESSION['user_id'] = $userId;
                    $_SESSION['user_username'] = $userUsername;
                    $_SESSION['user_nombre'] = $userNombre;
                    $_SESSION['user_email'] = $userEmail;
                    $_SESSION['user_rol'] = $userRol;

                    return true;
                }
            }
        } else {
            error_log("Usuario no encontrado: $username");
        }

        return false;
    } catch (PDOException $e) {
        error_log("Error en login: " . $e->getMessage());
        return false;
    }
}

/**
 * Cierra la sesión del usuario
 */
function logout() {
    // Eliminar todas las variables de sesión
    $_SESSION = array();

    // Destruir la sesión
    session_destroy();

    // Redirigir a la página de login
    header('Location: login.php');
    exit;
}

/**
 * Registra un nuevo usuario
 * @param array $userData
 * @return bool|string
 */
function registerUser($userData) {
    try {
        $pdo = getConexion();

        // Verificar si el usuario ya existe
        $sql = "SELECT COUNT(*) FROM usuarios WHERE username = :username";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':username', $userData['username']);
        $stmt->execute();

        if ($stmt->fetchColumn() > 0) {
            return "El nombre de usuario ya está en uso";
        }

        // Encriptar la contraseña
        $hashedPassword = password_hash($userData['password'], PASSWORD_DEFAULT);

        // Insertar el nuevo usuario
        $sql = "INSERT INTO usuarios (username, password, nombre, email, rol) VALUES (:username, :password, :nombre, :email, :rol)";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':username', $userData['username']);
        $stmt->bindParam(':password', $hashedPassword);
        $stmt->bindParam(':nombre', $userData['nombre']);
        $stmt->bindParam(':email', $userData['email']);
        $stmt->bindParam(':rol', $userData['rol']);

        if ($stmt->execute()) {
            return true;
        } else {
            return "Error al registrar el usuario";
        }
    } catch (PDOException $e) {
        error_log("Error en registerUser: " . $e->getMessage());
        return "Error: " . $e->getMessage();
    }
}

/**
 * Obtiene todos los usuarios
 * @return array|bool
 */
function getAllUsers() {
    try {
        $pdo = getConexion();

        $sql = "SELECT id, username, nombre, email, rol, fecha_creacion, activo FROM usuarios ORDER BY id";
        $stmt = $pdo->query($sql);

        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Convertir nombres de columnas a minúsculas para mantener consistencia
        $normalizedUsers = [];
        foreach ($users as $user) {
            $normalizedUser = [];
            foreach ($user as $key => $value) {
                $normalizedUser[strtolower($key)] = $value;
            }
            $normalizedUsers[] = $normalizedUser;
        }

        return $normalizedUsers;
    } catch (PDOException $e) {
        error_log("Error en getAllUsers: " . $e->getMessage());
        return false;
    }
}

/**
 * Obtiene un usuario por su ID
 * @param int $id
 * @return array|bool
 */
function getUserById($id) {
    try {
        $pdo = getConexion();

        $sql = "SELECT id, username, nombre, email, rol, activo FROM usuarios WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        // Convertir nombres de columnas a minúsculas para mantener consistencia
        if ($user) {
            $normalizedUser = [];
            foreach ($user as $key => $value) {
                $normalizedUser[strtolower($key)] = $value;
            }
            return $normalizedUser;
        }

        return false;
    } catch (PDOException $e) {
        error_log("Error en getUserById: " . $e->getMessage());
        return false;
    }
}

/**
 * Actualiza un usuario
 * @param array $userData
 * @return bool|string
 */
function updateUser($userData) {
    try {
        $pdo = getConexion();

        // Si se proporciona una nueva contraseña, encriptarla
        if (!empty($userData['password'])) {
            $hashedPassword = password_hash($userData['password'], PASSWORD_DEFAULT);

            $sql = "UPDATE usuarios SET username = :username, password = :password, nombre = :nombre, email = :email, rol = :rol, activo = :activo WHERE id = :id";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':password', $hashedPassword);
        } else {
            $sql = "UPDATE usuarios SET username = :username, nombre = :nombre, email = :email, rol = :rol, activo = :activo WHERE id = :id";
            $stmt = $pdo->prepare($sql);
        }

        $stmt->bindParam(':id', $userData['id']);
        $stmt->bindParam(':username', $userData['username']);
        $stmt->bindParam(':nombre', $userData['nombre']);
        $stmt->bindParam(':email', $userData['email']);
        $stmt->bindParam(':rol', $userData['rol']);
        $stmt->bindParam(':activo', $userData['activo']);

        if ($stmt->execute()) {
            return true;
        } else {
            return "Error al actualizar el usuario";
        }
    } catch (PDOException $e) {
        error_log("Error en updateUser: " . $e->getMessage());
        return "Error: " . $e->getMessage();
    }
}

/**
 * Elimina un usuario
 * @param int $id
 * @return bool|string
 */
function deleteUser($id) {
    try {
        $pdo = getConexion();

        $sql = "DELETE FROM usuarios WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $id);

        if ($stmt->execute()) {
            return true;
        } else {
            return "Error al eliminar el usuario";
        }
    } catch (PDOException $e) {
        error_log("Error en deleteUser: " . $e->getMessage());
        return "Error: " . $e->getMessage();
    }
}
?>
